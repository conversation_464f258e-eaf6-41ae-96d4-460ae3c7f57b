package com.carplus.subscribe.controller.dealer;

import carplus.common.enums.HeaderDefine;
import carplus.common.enums.etag.ETagFlow;
import carplus.common.enums.etag.ETagPayFlow;
import carplus.common.utils.DateUtils;
import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.model.cars.ProcessingOrder;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.etag.EtagCloseRequest;
import com.carplus.subscribe.model.etag.EtagInfoResponse;
import com.carplus.subscribe.model.order.LrentalContractRequest;
import com.carplus.subscribe.model.request.dealer.*;
import com.carplus.subscribe.model.response.dealer.DealerOrderQueryResponse;
import com.carplus.subscribe.model.response.dealer.DealerOrderResponse;
import com.carplus.subscribe.service.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static carplus.common.response.CarPlusCode.API_USE_INCORRECT;
import static com.carplus.subscribe.constant.CarPlusConstant.SEALAND_VIRTUAL_PLATE_NO;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional(transactionManager = "mysqlTransactionManager", isolation = Isolation.READ_COMMITTED)
class DealerOrderInternalControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private DealerOrderService dealerOrderService;
    @Autowired
    private CarsService carsService;
    @Autowired
    private CrsService crsService;
    @Autowired
    private ETagService eTagService;
    @Autowired
    private DealerOrderPriceInfoService dealerOrderPriceInfoService;
    @Autowired
    private ObjectMapper objectMapper;

    private final String HEADER_MEMBER_ID = "K2765";
    private static final String PLATE_NO = "RFA-6076";
    private static final String PLATE_NO_VIRTUAL = "RAA0020";
    private static final String PLATE_NO_NEW = "RFA-3978";
    private static final String ORDER_NO = "M202404188890";
    private static final String CANCEL_ORDER_NO = "20250224009";
    private static final String EXPECT_DEPART_STATION = "831";
    private static final String EXPECT_RETURN_STATION = "201";
    private static final String ID_NO = "A195970863";

    // ======================== Helper Methods ========================

    /**
     * 建立測試用的 DealerOrderCreateRequest
     */
    private DealerOrderCreateRequest buildDealerOrderCreateRequest(String orderNo, BuIdEnum buIdEnum) {
        DealerOrderCreateRequest request = new DealerOrderCreateRequest();
        request.setOrderNo(orderNo);
        request.setPlateNo(findAvailableCar(buIdEnum));
        request.setIsNewOrder(true);
        request.setSecurityDepositDate(Instant.now());
        request.setParentOrderNo(orderNo);
        request.setStage("1-1");
        request.setIsRegister(true);

        // 客戶資訊
        request.setCustomerInfo(buildCustomerInfo());

        // 訂閱資訊
        request.setSubscriptionInfo(buildSubscriptionInfo());

        return request;
    }

    /**
     * 建立測試用的客戶資訊
     */
    private DealerCustomerInfoForCreate buildCustomerInfo() {
        DealerCustomerInfoForCreate customerInfo = new DealerCustomerInfoForCreate();
        customerInfo.setUserName("Hans");
        customerInfo.setIdNo(ID_NO);
        customerInfo.setNationalCode("886");
        customerInfo.setMainCell("0912345678");
        customerInfo.setBirthDay("2000-01-01");
        customerInfo.setEmail("<EMAIL>");
        customerInfo.setCity(3);
        customerInfo.setArea(23);
        customerInfo.setAddress("經銷商訂單測試地址");
        return customerInfo;
    }

    /**
     * 建立測試用的訂閱資訊
     */
    private DealerSubscriptionInfo buildSubscriptionInfo() {
        DealerSubscriptionInfo subscriptionInfo = new DealerSubscriptionInfo();
        subscriptionInfo.setSecurityDeposit(10000);
        subscriptionInfo.setMonthlyFee(8800);
        subscriptionInfo.setActualMileageRate(2.8);
        subscriptionInfo.setOriginalMileageRate(3.0);
        subscriptionInfo.setExpectDepartStation(EXPECT_DEPART_STATION);
        subscriptionInfo.setExpectReturnStation(EXPECT_RETURN_STATION);
        subscriptionInfo.setExpectDepartDate(Instant.now().plus(1, ChronoUnit.DAYS));
        subscriptionInfo.setExpectReturnDate(Instant.now().plus(90, ChronoUnit.DAYS));
        subscriptionInfo.setSubscribeMonth(3);
        subscriptionInfo.setTotalAmt(26820);
        subscriptionInfo.setMonthlyFeeDiscount(0);
        subscriptionInfo.setMileageRateDiscount(0.5);
        subscriptionInfo.setPrepaidMonths(1);
        subscriptionInfo.setPrepaidMileage(3000);
        subscriptionInfo.setPrepaidMileageDiscount(0);
        subscriptionInfo.setFlexibleMileage(0);
        subscriptionInfo.setActualMileageUsed(0);
        subscriptionInfo.setOffsetMileage1(0);
        subscriptionInfo.setOffsetMileage2(0);
        subscriptionInfo.setPrepaidMileageFee(8400);
        return subscriptionInfo;
    }

    /**
     * 建立測試用的出車請求
     */
    private DealerOrderDepartRequest buildDepartRequest(String orderNo, String plateNo) {
        DealerOrderDepartRequest request = new DealerOrderDepartRequest();
        request.setOrderNo(orderNo);
        request.setPlateNo(plateNo);

        DealerSubscriptionInfoForDepart subscriptionInfo = new DealerSubscriptionInfoForDepart();
        subscriptionInfo.setDepartStation("35");
        subscriptionInfo.setDepartDate(Instant.now().plus(1, ChronoUnit.DAYS));
        subscriptionInfo.setPaidAmt(10000);
        subscriptionInfo.setBeginAmt(34800);
        request.setSubscriptionInfo(subscriptionInfo);

        return request;
    }

    /**
     * 建立測試用的結案請求
     */
    private DealerOrderCloseRequest buildCloseRequest(String orderNo, String plateNo) {
        DealerOrderCloseRequest request = new DealerOrderCloseRequest();
        request.setOrderNo(orderNo);
        request.setPlateNo(plateNo);

        DealerSubscriptionInfoForClose subscriptionInfo = new DealerSubscriptionInfoForClose();
        subscriptionInfo.setReturnStation("35");
        subscriptionInfo.setReturnDate(Instant.now());
        subscriptionInfo.setIsReturned(false);
        subscriptionInfo.setCloseAmt(-34800);
        request.setSubscriptionInfo(subscriptionInfo);

        return request;
    }

    /**
     * 建立測試用的取消請求
     */
    private DealerOrderCancelRequest buildCancelRequest(String orderNo, String cancelRemark) {
        DealerOrderCancelRequest request = new DealerOrderCancelRequest();
        request.setOrderNo(orderNo);
        request.setCancelDate(Instant.now().plus(30, ChronoUnit.MINUTES));
        request.setCancelRemark(cancelRemark);

        DealerSubscriptionInfoForCancel subscriptionInfo = new DealerSubscriptionInfoForCancel();
        subscriptionInfo.setTotalAmt(26820);
        request.setSubscriptionInfo(subscriptionInfo);

        return request;
    }

    /**
     * 建立測試用的更新請求
     */
    private DealerOrderUpdateRequest buildUpdateRequest(String orderNo, String plateNo) {
        DealerOrderUpdateRequest request = new DealerOrderUpdateRequest();
        request.setOrderNo(orderNo);
        request.setPlateNo(plateNo);

        DealerCustomerInfoAllOptional customerInfo = new DealerCustomerInfoAllOptional();
        customerInfo.setUserName("Hans123");
        customerInfo.setIdNo(ID_NO);
        request.setCustomerInfo(customerInfo);

        DealerSubscriptionInfoForUpdate subscriptionInfo = new DealerSubscriptionInfoForUpdate();
        subscriptionInfo.setSecurityDeposit(12000);
        subscriptionInfo.setMonthlyFee(8800);
        subscriptionInfo.setActualMileageRate(2.8);
        subscriptionInfo.setOriginalMileageRate(3.0);
        subscriptionInfo.setExpectDepartStation(EXPECT_DEPART_STATION);
        subscriptionInfo.setExpectReturnStation("201");
        subscriptionInfo.setExpectDepartDate(Instant.parse("2024-04-17T09:30:10.404Z"));
        subscriptionInfo.setExpectReturnDate(Instant.parse("2024-04-27T09:30:10.404Z"));
        subscriptionInfo.setSubscribeMonth(3);
        subscriptionInfo.setTotalAmt(26820);
        subscriptionInfo.setMonthlyFeeDiscount(0);
        subscriptionInfo.setMileageRateDiscount(0.5);
        subscriptionInfo.setPrepaidMonths(3);
        subscriptionInfo.setPrepaidMileage(3000);
        subscriptionInfo.setPrepaidMileageDiscount(0);
        subscriptionInfo.setFlexibleMileage(0);
        subscriptionInfo.setActualMileageUsed(0);
        subscriptionInfo.setOffsetMileage1(0);
        subscriptionInfo.setOffsetMileage2(0);
        subscriptionInfo.setPrepaidMileageFee(8400);
        request.setSubscriptionInfo(subscriptionInfo);

        return request;
    }

    /**
     * 執行 HTTP POST 請求的通用方法
     */
    private ResultActions performPost(String url, Object request) throws Exception {
        return mockMvc.perform(post(url)
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)));
    }

    /**
     * 執行 HTTP PATCH 請求的通用方法
     */
    private ResultActions performPatch(String url, Object request) throws Exception {
        return mockMvc.perform(patch(url)
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .content(objectMapper.writeValueAsString(request)));
    }

    /**
     * 執行 HTTP GET 請求的通用方法
     */
    private ResultActions performGet(String url, Map<String, String> params) throws Exception {
        MockHttpServletRequestBuilder builder = get(url);
        if (params != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.param(entry.getKey(), entry.getValue());
            }
        }
        return mockMvc.perform(builder);
    }

    /**
     * 驗證車輛狀態和訂單編號
     */
    private void verifyCarStatusAndBookingOrderNo(Cars cars, CarDefine.CarStatus carStatus, String expectedBookingOrderNo) {
        assertEquals(carStatus.getCode(), cars.getCarStatus());
        assertEquals(expectedBookingOrderNo, cars.getBookingOrderNo());
    }

    /**
     * 驗證成功的經銷商訂單建立結果
     */
    private void verifySuccessfulDealerOrderCreation(ResultActions result) throws Exception {
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.customerInfo.userName").isString())
            .andExpect(jsonPath("$.data.subscriptionInfo.securityDeposit").isNumber())
            .andExpect(jsonPath("$.data.dealerName").value(HeaderDefine.SystemKind.SEALAND))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CREATE.getCode()))
            .andExpect(jsonPath("$.data.isAudit").value(true))
            .andExpect(jsonPath("$.data.isPaySecurityDeposit").value(true))
            .andExpect(jsonPath("$.data.dealerUserId").isNumber())
            .andExpect(jsonPath("$.data.dealerUserId", Matchers.not(0)));
    }

    /**
     * 驗證成功的出車結果
     */
    private void verifySuccessfulDepart(ResultActions result) throws Exception {
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.GOING.getCode()))
            .andExpect(jsonPath("$.data.departStation").value("35"))
            .andExpect(jsonPath("$.data.departDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.beginAmt").isNumber());
    }

    /**
     * 驗證成功的結案結果
     */
    private void verifySuccessfulClose(ResultActions result) throws Exception {
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.COMPLETE.getCode()))
            .andExpect(jsonPath("$.data.returnStation").value("35"))
            .andExpect(jsonPath("$.data.returnDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.closeAmt").isNumber());
    }

    /**
     * 驗證成功的取消結果
     */
    private void verifySuccessfulCancel(ResultActions result, String expectedRemark) throws Exception {
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CANCEL.getCode()))
            .andExpect(jsonPath("$.data.isCancel").value(true));

        if (expectedRemark != null) {
            result.andExpect(jsonPath("$.data.cancelRemark").value(expectedRemark));
        }
    }

    /**
     * 驗證 Bad Request 錯誤
     */
    private void verifyBadRequestError(ResultActions result, String expectedMessage) throws Exception {
        result.andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.statusCode").value(API_USE_INCORRECT.getCode()))
            .andExpect(jsonPath("$.message").value(containsString(expectedMessage)))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    /**
     * 建立完整的測試訂單（建立 + 長租契約）
     */
    private DealerOrderQueryResponse createCompleteTestOrder(String orderNo, BuIdEnum buIdEnum) {
        return createCompleteTestOrder(orderNo, buIdEnum, null, null);
    }

    private DealerOrderQueryResponse createCompleteTestOrder(String orderNo, BuIdEnum buIdEnum, String changeField, Object changeValue) {
        DealerOrderCreateRequest request = buildDealerOrderCreateRequest(orderNo, buIdEnum);

        // 如果需要修改特定欄位
        if (changeField != null && changeValue != null) {
            updateRequestField(request, changeField, changeValue);
        }

        DealerOrderQueryResponse dealerOrder = dealerOrderService.createDealerOrder(request, false);

        // 建立長租契約
        createLrentalContract(dealerOrder);

        return dealerOrder;
    }

    /**
     * 建立長租契約
     */
    private void createLrentalContract(DealerOrderQueryResponse dealerOrder) {
        LrentalContractRequest request = new LrentalContractRequest();
        request.setOrderNo(dealerOrder.getOrderNo());
        request.setReplaceCodes(Collections.singletonList("5"));
        request.setMemo(String.format("格上官網／新單／中古車\\n%s：%s - %s（租期%d個月）\\n",
            dealerOrder.getOrderNo(),
            DateUtils.toDateString(Date.from(dealerOrder.getSubscriptionInfo().getExpectDepartDate()), "yyyy/MM/dd"),
            DateUtils.toDateString(Date.from(dealerOrder.getSubscriptionInfo().getExpectReturnDate()), "yyyy/MM/dd"),
            dealerOrder.getSubscriptionInfo().getSubscribeMonth()));
        dealerOrderService.createLrentalContract(request, HEADER_MEMBER_ID);
    }

    /**
     * 執行出車操作
     */
    private DealerOrder performDepartOperation(String orderNo, String plateNo) {
        DealerOrderDepartRequest request = buildDepartRequest(orderNo, plateNo);
        return dealerOrderService.departDealerOrder(request, HEADER_MEMBER_ID, null);
    }

    /**
     * 執行 ETag 關閉操作
     */
    private void performEtagClose(String orderNo) {
        EtagCloseRequest request = new EtagCloseRequest();
        request.setReturnDate(Instant.now());
        dealerOrderService.etagReturn(orderNo, HEADER_MEMBER_ID, request);
    }

    /**
     * 更新請求物件的指定欄位
     */
    private void updateRequestField(Object request, String fieldName, Object fieldValue) {
        try {
            Field field = request.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(request, fieldValue);
        } catch (Exception e) {
            throw new RuntimeException("Failed to update field: " + fieldName, e);
        }
    }

    /**
     * 站點驗證的測試輔助方法 - 用於建立訂單時的站點驗證
     */
    private void testStationValidation(String stationField, String invalidStation, String expectedErrorMessage) throws Exception {
        DealerOrderCreateRequest request = buildDealerOrderCreateRequest("M202501010001", BuIdEnum.subscribe);
        updateRequestField(request.getSubscriptionInfo(), stationField, invalidStation);

        ResultActions result = performPost("/internal/subscribe/dealerOrder", request);
        verifyBadRequestError(result, expectedErrorMessage);
    }

    /**
     * 建立用於取消訂單測試的請求（需要不同的訂單編號避免衝突）
     */
    private DealerOrderCancelRequest buildCancelRequestWithOrderNo(String orderNo, String plateNo, String cancelRemark) {
        DealerOrderCancelRequest request = new DealerOrderCancelRequest();
        request.setOrderNo(orderNo);
        request.setPlateNo(plateNo);
        request.setCancelDate(Instant.now().plus(30, ChronoUnit.MINUTES));
        request.setCancelRemark(cancelRemark);

        DealerSubscriptionInfoForCancel subscriptionInfo = new DealerSubscriptionInfoForCancel();
        subscriptionInfo.setTotalAmt(26820);
        request.setSubscriptionInfo(subscriptionInfo);

        return request;
    }

    /**
     * 建立用於出車測試的請求（需要不同的訂單編號避免衝突）
     */
    private DealerOrderDepartRequest buildDepartRequestWithOrderNo(String orderNo, String plateNo) {
        DealerOrderDepartRequest request = new DealerOrderDepartRequest();
        request.setOrderNo(orderNo);
        request.setPlateNo(plateNo);

        DealerSubscriptionInfoForDepart subscriptionInfo = new DealerSubscriptionInfoForDepart();
        subscriptionInfo.setDepartStation("35");
        subscriptionInfo.setDepartDate(Instant.now().plus(1, ChronoUnit.DAYS));
        subscriptionInfo.setPaidAmt(10000);
        subscriptionInfo.setBeginAmt(34800);
        request.setSubscriptionInfo(subscriptionInfo);

        return request;
    }

    /**
     * 建立用於結案測試的請求（需要不同的訂單編號避免衝突）
     */
    private DealerOrderCloseRequest buildCloseRequestWithOrderNo(String orderNo, String plateNo) {
        DealerOrderCloseRequest request = new DealerOrderCloseRequest();
        request.setOrderNo(orderNo);
        request.setPlateNo(plateNo);

        DealerSubscriptionInfoForClose subscriptionInfo = new DealerSubscriptionInfoForClose();
        subscriptionInfo.setReturnStation("35");
        subscriptionInfo.setReturnDate(Instant.now());
        subscriptionInfo.setIsReturned(false);
        subscriptionInfo.setCloseAmt(-34800);
        request.setSubscriptionInfo(subscriptionInfo);

        return request;
    }

    /**
     * 批次處理尋找符合條件的車牌號碼
     */
    private String findAvailableCar(BuIdEnum buIdEnum) {
        List<Cars> idleCars = carsService.getIdleCar();
        String foundPlateNo = null;
        int batchSize = 100;

        for (int i = 0; i < idleCars.size(); i += batchSize) {
            List<String> plateNos = idleCars.stream()
                .skip(i)
                .limit(batchSize)
                .map(Cars::getPlateNo)
                .collect(Collectors.toList());

            Map<String, CarBaseInfoSearchResponse> carResponses = crsService.getCars(plateNos);

            Optional<Cars> matchedCar = idleCars.subList(i, Math.min(i + batchSize, idleCars.size())).stream()
                .filter(car -> {
                    CarBaseInfoSearchResponse carBaseInfoSearchResponse = carResponses.get(car.getPlateNo());
                    return carBaseInfoSearchResponse != null
                        && buIdEnum.getCode().equals(carBaseInfoSearchResponse.getBuId())
                        && Arrays.asList(CarDefine.Launched.open, CarDefine.Launched.close).contains(car.getLaunched());
                })
                .findFirst();

            if (matchedCar.isPresent()) {
                foundPlateNo = matchedCar.get().getPlateNo();
                break;
            }
        }

        assertNotNull(foundPlateNo, "No available car found for " + buIdEnum);
        return foundPlateNo;
    }

    // ======================== Test Methods ========================

    @Test
    void createDealerOrder() throws Exception {
        DealerOrderCreateRequest request = buildDealerOrderCreateRequest(ORDER_NO, BuIdEnum.subscribe);
        ResultActions result = performPost("/internal/subscribe/dealerOrder", request);

        verifySuccessfulDealerOrderCreation(result);

        MvcResult mvcResult = result.andReturn();
        DealerOrderQueryResponse response = objectMapper.treeToValue(
            objectMapper.readTree(mvcResult.getResponse().getContentAsString()).get("data"),
            DealerOrderQueryResponse.class
        );

        Cars cars = carsService.findByPlateNo(response.getPlateNo());
        verifyCarStatusAndBookingOrderNo(cars, CarDefine.CarStatus.Subscribed, ORDER_NO);
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    void testOperatingTwoDealerOrdersWithSameUser() throws Exception {
        String firstOrderNo = ORDER_NO;
        String secondOrderNo = "M202404188889";

        DealerOrderCreateRequest dealerOrderCreateRequest = buildDealerOrderCreateRequest(firstOrderNo, BuIdEnum.subscribe);

        // Create first dealer order
        MvcResult createFirstDealerOrderResult = performPost("/internal/subscribe/dealerOrder", dealerOrderCreateRequest).andReturn();

        DealerOrderQueryResponse firstDealerOrderQueryResponse = objectMapper.treeToValue(
            objectMapper.readTree(createFirstDealerOrderResult.getResponse().getContentAsString()).get("data"),
            DealerOrderQueryResponse.class
        );

        Cars cars = carsService.findByPlateNo(firstDealerOrderQueryResponse.getPlateNo());
        verifyCarStatusAndBookingOrderNo(cars, CarDefine.CarStatus.Subscribed, firstOrderNo);

        Thread.sleep(1000);

        // first dealer order 建立長租契約
        createLrentalContract(firstDealerOrderQueryResponse);

        Thread.sleep(1000);

        // first dealer order depart
        DealerOrder firstDepartedDealerOrder = performDepartOperation(firstOrderNo, firstDealerOrderQueryResponse.getPlateNo());

        assertEquals(ContractStatus.GOING.getCode(), firstDepartedDealerOrder.getOrderStatus());
        assertEquals(CarDefine.CarStatus.BizOut.getCode(), carsService.getCarInfo(firstDealerOrderQueryResponse.getPlateNo()).getCarStatus());

        Thread.sleep(1000);

        // Create second dealer order
        dealerOrderCreateRequest.setOrderNo(secondOrderNo);
        MvcResult createSecondDealerOrderResult = performPost("/internal/subscribe/dealerOrder", dealerOrderCreateRequest).andReturn();

        DealerOrderQueryResponse secondDealerOrderQueryResponse = objectMapper.treeToValue(
            objectMapper.readTree(createSecondDealerOrderResult.getResponse().getContentAsString()).get("data"),
            DealerOrderQueryResponse.class
        );

        CarResponse carInfo = carsService.getCarInfo(secondDealerOrderQueryResponse.getPlateNo());
        // 因為已有一筆出車訂單，故驗證車輛狀態為 BizOut
        assertEquals(CarDefine.CarStatus.BizOut.getCode(), carInfo.getCarStatus());

        List<ProcessingOrder> processOrders = carInfo.getProcessOrders();
        assertEquals(2, processOrders.size());
        assertThat(processOrders.stream().map(ProcessingOrder::getOrderNo).collect(Collectors.toList())).containsExactlyInAnyOrder(firstOrderNo, secondOrderNo);

        Thread.sleep(1000);

        // second dealer order 建立長租契約
        createLrentalContract(secondDealerOrderQueryResponse);

        Thread.sleep(1000);

        // second dealer order depart
        DealerOrder secondDepartedDealerOrder = performDepartOperation(secondOrderNo, secondDealerOrderQueryResponse.getPlateNo());

        assertEquals(ContractStatus.GOING.getCode(), secondDepartedDealerOrder.getOrderStatus());
        assertEquals(CarDefine.CarStatus.BizOut.getCode(), carsService.getCarInfo(secondDealerOrderQueryResponse.getPlateNo()).getCarStatus());

        Thread.sleep(1000);

        // close first dealer order
        performEtagClose(firstOrderNo);

        DealerOrderCloseRequest dealerOrderCloseRequest = buildCloseRequestWithOrderNo(firstOrderNo, firstDealerOrderQueryResponse.getPlateNo());
        dealerOrderCloseRequest.getSubscriptionInfo().setCloseAmt(500);
        dealerOrderCloseRequest.getSubscriptionInfo().setIsReturned(true);

        performPatch("/internal/subscribe/dealerOrder/close", dealerOrderCloseRequest)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.COMPLETE.getCode()));

        CarResponse carInfoAfterFirstOrderClosed = carsService.getCarInfo(secondDealerOrderQueryResponse.getPlateNo());
        // 因為第二筆訂單還是出車中，所以車輛狀態應為 BizOut
        assertEquals(CarDefine.CarStatus.BizOut.getCode(), carInfoAfterFirstOrderClosed.getCarStatus());
        assertEquals(1, carInfoAfterFirstOrderClosed.getProcessOrders().size());

        // close second dealer order
        performEtagClose(secondOrderNo);

        DealerOrderCloseRequest secondDealerOrderCloseRequest = buildCloseRequestWithOrderNo(secondOrderNo, secondDealerOrderQueryResponse.getPlateNo());
        secondDealerOrderCloseRequest.getSubscriptionInfo().setCloseAmt(500);
        secondDealerOrderCloseRequest.getSubscriptionInfo().setIsReturned(true);

        performPatch("/internal/subscribe/dealerOrder/close", secondDealerOrderCloseRequest)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.COMPLETE.getCode()));

        // 兩筆訂單皆已還車，車輛狀態應為 Free
        CarResponse carInfoAfterSecondOrderClosed = carsService.getCarInfo(secondDealerOrderQueryResponse.getPlateNo());
        assertEquals(CarDefine.CarStatus.Free.getCode(), carInfoAfterSecondOrderClosed.getCarStatus());
        assertTrue(carInfoAfterSecondOrderClosed.getProcessOrders().isEmpty());
    }

    @Test
    void createDealerOrder_VirtualCar() throws Exception {
        DealerOrderCreateRequest request = buildDealerOrderCreateRequest(ORDER_NO, BuIdEnum.subscribe);
        request.setPlateNo(PLATE_NO_VIRTUAL);

        ResultActions result = performPost("/internal/subscribe/dealerOrder", request);
        verifySuccessfulDealerOrderCreation(result);

        Cars cars = carsService.findByPlateNo(PLATE_NO_VIRTUAL);
        verifyCarStatusAndBookingOrderNo(cars, CarDefine.CarStatus.Free, ORDER_NO);
    }

    @Test
    void createDealerOrder_PlateNoExistsInCrsOnly() throws Exception {
        // 使用一個只存在於 CRS 的車牌號碼
        String crsOnlyPlateNo = "GGG-8000";

        // 確認車牌不存在於訂閱車籍
        assert carsService.findByPlateNo(crsOnlyPlateNo) == null;
        // 確認車牌存在於 CRS
        assert crsService.getCar(crsOnlyPlateNo) != null;

        DealerOrderCreateRequest request = buildDealerOrderCreateRequest(ORDER_NO, BuIdEnum.subscribe);
        request.setPlateNo(crsOnlyPlateNo);

        ResultActions result = performPost("/internal/subscribe/dealerOrder", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(crsOnlyPlateNo))
            .andExpect(jsonPath("$.data.customerInfo.userName").isString())
            .andExpect(jsonPath("$.data.subscriptionInfo.securityDeposit").isNumber())
            .andExpect(jsonPath("$.data.dealerName").value(HeaderDefine.SystemKind.SEALAND))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CREATE.getCode()))
            .andExpect(jsonPath("$.data.isAudit").value(true))
            .andExpect(jsonPath("$.data.isPaySecurityDeposit").value(true));

        // 驗證車輛狀態
        Cars car = carsService.findByPlateNo(crsOnlyPlateNo);
        // 確認車輛已被新增到訂閱車籍
        assert car != null;
        verifyCarStatusAndBookingOrderNo(car, CarDefine.CarStatus.Subscribed, ORDER_NO);
    }

    @Test
    void createDealerOrder_NonExistentPlateNo_ShouldUseSeaLandVirtualPlateNo() throws Exception {
        // 使用一個不存在的車牌號碼
        String nonExistentPlateNo = "XXX-9999";

        // 檢查訂閱車籍是否存在該車牌號碼
        assert carsService.findByPlateNo(nonExistentPlateNo) == null;
        // 檢查 CRS 中是否存在該車牌號碼
        assert crsService.getCar(nonExistentPlateNo) == null;

        DealerOrderCreateRequest request = buildDealerOrderCreateRequest(ORDER_NO, BuIdEnum.subscribe);
        request.setPlateNo(nonExistentPlateNo);

        ResultActions result = performPost("/internal/subscribe/dealerOrder", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(SEALAND_VIRTUAL_PLATE_NO))
            .andExpect(jsonPath("$.data.customerInfo.userName").isString())
            .andExpect(jsonPath("$.data.subscriptionInfo.securityDeposit").isNumber())
            .andExpect(jsonPath("$.data.dealerName").value(HeaderDefine.SystemKind.SEALAND))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CREATE.getCode()))
            .andExpect(jsonPath("$.data.isAudit").value(true))
            .andExpect(jsonPath("$.data.isPaySecurityDeposit").value(true));

        // 驗證虛擬車輛狀態
        Cars virtualCar = carsService.findByPlateNo(SEALAND_VIRTUAL_PLATE_NO);
        verifyCarStatusAndBookingOrderNo(virtualCar, CarDefine.CarStatus.Free, ORDER_NO);
    }

    @Test
    void createDealerOrder_Failure_OrderNoIsEmpty() throws Exception {
        DealerOrderCreateRequest request = buildDealerOrderCreateRequest("", BuIdEnum.subscribe);
        ResultActions result = performPost("/internal/subscribe/dealerOrder", request);
        verifyBadRequestError(result, "訂單編號不可為空");
    }

    @Test
    void updateDealerOrder() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        DealerOrderUpdateRequest request = buildUpdateRequest(ORDER_NO, PLATE_NO);
        ResultActions result = performPatch("/internal/subscribe/dealerOrder", request);

        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.customerInfo.userName").value("Hans123"))
            .andExpect(jsonPath("$.data.subscriptionInfo.securityDeposit").value(12000))
            .andExpect(jsonPath("$.data.dealerUserId").isNumber())
            .andExpect(jsonPath("$.data.dealerUserId").value(Matchers.not(0)));
    }

    @Test
    void updateDealerOrder_RemoveCustomerInfoNoChangeFields() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        DealerOrderUpdateRequest request = buildUpdateRequest(ORDER_NO, PLATE_NO);
        // 移除不需要變更的欄位，只保留需要更新的欄位
        DealerCustomerInfoAllOptional customerInfo = new DealerCustomerInfoAllOptional();
        customerInfo.setUserName("Hans123");
        customerInfo.setIdNo(ID_NO);
        request.setCustomerInfo(customerInfo);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.customerInfo.userName").value("Hans123"))
            .andExpect(jsonPath("$.data.subscriptionInfo.securityDeposit").value(12000))
            .andExpect(jsonPath("$.data.dealerUserId").isNumber())
            .andExpect(jsonPath("$.data.dealerUserId").value(Matchers.not(0)));
    }

    @Test
    void updateDealerOrder_Failure_ChangeValidIdNo() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        DealerOrderUpdateRequest request = buildUpdateRequest(ORDER_NO, PLATE_NO);
        // 嘗試修改身分證號碼
        request.getCustomerInfo().setIdNo("F172455322");

        ResultActions result = performPatch("/internal/subscribe/dealerOrder", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(DEALER_USER_ID_NO_CAN_NOT_CHANGE.getCode()))
            .andExpect(jsonPath("$.message").value(DEALER_USER_ID_NO_CAN_NOT_CHANGE.getMsg()));
    }

    @Test
    void updateDealerOrder_ChangeValidDealerUserId() throws Exception {
        createCompleteTestOrder("M202406070003", BuIdEnum.subscribe);

        DealerOrderUpdateRequest request = buildUpdateRequest("M202406070003", "RDZ-7108");
        request.getCustomerInfo().setUserName("ValidIdTest");
        request.getCustomerInfo().setIdNo("F172455322");

        ResultActions result = performPatch("/internal/subscribe/dealerOrder", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.customerInfo.userName").value("ValidIdTest"))
            .andExpect(jsonPath("$.data.dealerUserId").isNumber())
            .andExpect(jsonPath("$.data.dealerUserId").value(Matchers.not(0)));
    }

    @Test
    @Rollback(false)
    void updateDealerOrder_ChangePlateNo() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        DealerOrderUpdateRequest request = buildUpdateRequest(ORDER_NO, PLATE_NO_NEW);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(PLATE_NO_NEW))
            .andExpect(jsonPath("$.data.customerInfo.userName").value("Hans123"))
            .andExpect(jsonPath("$.data.subscriptionInfo.securityDeposit").value(12000));

        verifyChangePlateNoResult(ContractStatus.CREATE);
    }

    private void verifyChangePlateNoResult(ContractStatus orderStatusToChange) {
        Cars oriCar = carsService.findByPlateNo(PLATE_NO);
        CarBaseInfoSearchResponse oriCarBase = crsService.getCar(PLATE_NO);
        // 驗證原車 crsCarNo 是否更新
        assertEquals(oriCarBase.getCarNo(), oriCar.getCrsCarNo());
        verifyCarStatusAndBookingOrderNo(oriCar, CarDefine.CarStatus.Free, null);

        Cars newCar = carsService.findByPlateNo(PLATE_NO_NEW);
        CarBaseInfoSearchResponse newCarBase = crsService.getCar(PLATE_NO_NEW);
        // 驗證新車 crsCarNo 是否更新
        assertEquals(newCarBase.getCarNo(), newCar.getCrsCarNo());
        switch (orderStatusToChange) {
            case CREATE:
                verifyCarStatusAndBookingOrderNo(newCar, CarDefine.CarStatus.Subscribed, ORDER_NO);
                break;
            case GOING:
                verifyCarStatusAndBookingOrderNo(newCar, CarDefine.CarStatus.BizOut, ORDER_NO);
                break;
            default:
                throw new IllegalArgumentException("Unsupported order status: " + orderStatusToChange);
        }
    }

    @Test
    public void updateDealerOrder_Failure_OrderNotFound() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        String nonExistentOrderNo = "nonExistentOrderNo";
        DealerOrderUpdateRequest request = buildUpdateRequest(nonExistentOrderNo, PLATE_NO);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(DEALER_ORDER_NOT_FOUND.getCode()))
            .andExpect(jsonPath("$.message").value(containsString("找不到經銷商訂單")))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    public void updateDealerOrder_Failure_PlateNoIsNull() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        DealerOrderUpdateRequest request = buildUpdateRequest(ORDER_NO, null);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder", request);
        verifyBadRequestError(result, "車牌號碼不可為空");
    }

    @Test
    public void departDealerOrder() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        DealerOrderDepartRequest request = buildDepartRequest(ORDER_NO, PLATE_NO);
        ResultActions result = performPatch("/internal/subscribe/dealerOrder/depart", request);

        verifySuccessfulDepart(result);

        Cars cars = carsService.findByPlateNo(PLATE_NO);
        verifyCarStatusAndBookingOrderNo(cars, CarDefine.CarStatus.BizOut, ORDER_NO);

        List<EtagInfoResponse> responses = eTagService.getETagIntoByOrderNo(ORDER_NO);
        responses.forEach(response -> {
            assertEquals(ORDER_NO, response.getOrderNo());
            assertEquals(ETagFlow.DEPART_SUCCESS.getCode(), response.getETagFlow());
            assertTrue(response.isUploaded());
        });
    }

    @Test
    public void departDealerOrder_VirtualCar() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe, "plateNo", PLATE_NO_VIRTUAL);

        DealerOrderDepartRequest request = buildDepartRequestWithOrderNo(ORDER_NO, PLATE_NO_VIRTUAL);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/depart", request);
        verifySuccessfulDepart(result);

        Cars cars = carsService.findByPlateNo(PLATE_NO_VIRTUAL);
        verifyCarStatusAndBookingOrderNo(cars, CarDefine.CarStatus.Free, ORDER_NO);
    }

    @Test
    public void departDealerOrder_ChangePlateNo() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        DealerOrderDepartRequest request = buildDepartRequestWithOrderNo(ORDER_NO, PLATE_NO_NEW);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/depart", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(PLATE_NO_NEW))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.GOING.getCode()));

        verifyChangePlateNoResult(ContractStatus.GOING);
    }

    @Test
    public void departDealerOrder_Failure_ChangeSeaLandVirtualPlateNo() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        // 出車時使用虛擬車號
        DealerOrderDepartRequest request = buildDepartRequestWithOrderNo(ORDER_NO, SEALAND_VIRTUAL_PLATE_NO);

        ResultActions result = mockMvc.perform(patch("/internal/subscribe/dealerOrder/depart")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
            .header(CarPlusConstant.AUTH_HEADER_SYSTEM_KIND, CsatOrderSource.SEALAND.name())
            .content(objectMapper.writeValueAsString(request)));

        result.andExpect(jsonPath("$.statusCode").value(DEALER_VIRTUAL_PLATE_NO_NOT_ALLOW_DEPART.getCode()));
    }

    @Test
    public void departDealerOrder_Failure_OrderStatusIsNotGoing() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        DealerOrderDepartRequest request = buildDepartRequest(ORDER_NO, PLATE_NO);
        // 設定錯誤的訂單狀態
        updateRequestField(request, "orderStatus", ContractStatus.CREATE.getCode());

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/depart", request);
        verifyBadRequestError(result, "訂單狀態參數不為出車狀態，不可執行出車API");
    }

    @Test
    public void departDealerOrder_OrderStatusIsNull() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        DealerOrderDepartRequest request = buildDepartRequest(ORDER_NO, PLATE_NO);
        updateRequestField(request, "orderStatus", null);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/depart", request);
        verifySuccessfulDepart(result);
    }

    @Test
    public void departDealerOrder_Failure_DepartStationIsNull() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        DealerOrderDepartRequest request = buildDepartRequest(ORDER_NO, PLATE_NO);
        request.getSubscriptionInfo().setDepartStation(null);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/depart", request);
        verifyBadRequestError(result, "實際出車站點不可為空");
    }

    @Test
    public void closeDealerOrder() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);
        performDepartOperation(ORDER_NO, PLATE_NO);
        performEtagClose(ORDER_NO);

        DealerOrderCloseRequest request = buildCloseRequest(ORDER_NO, PLATE_NO);
        ResultActions result = performPatch("/internal/subscribe/dealerOrder/close", request);

        verifySuccessfulClose(result);

        List<EtagInfoResponse> responses = eTagService.getETagIntoByOrderNo(ORDER_NO);
        responses.forEach(response -> {
            assertEquals(ORDER_NO, response.getOrderNo());
            assertEquals(ETagFlow.RETURN_SUCCESS.getCode(), response.getETagFlow());
            assertEquals(ETagPayFlow.DONE.getCode(), response.getETagPayFlow());
            Assertions.assertNotNull(response.getDealerOrderPriceInfoId());
            assertTrue(response.isUploaded());
            Assertions.assertNotNull(response.getReturnDate());
        });
    }

    @Test
    public void closeDealerOrder_OrderStatusIsNull() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);
        performDepartOperation(ORDER_NO, PLATE_NO);
        performEtagClose(ORDER_NO);

        DealerOrderCloseRequest request = buildCloseRequest(ORDER_NO, PLATE_NO);
        updateRequestField(request, "orderStatus", null);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/close", request);
        verifySuccessfulClose(result);
    }

    @Test
    public void closeDealerOrder_Failure_ReturnStationIsNull() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);
        performDepartOperation(ORDER_NO, PLATE_NO);
        performEtagClose(ORDER_NO);

        DealerOrderCloseRequest request = buildCloseRequest(ORDER_NO, PLATE_NO);
        request.getSubscriptionInfo().setReturnStation(null);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/close", request);
        verifyBadRequestError(result, "實際還車站點不可為空");
    }

    @Test
    public void closeDealerOrder_IsReturnedTrue() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);
        performDepartOperation(ORDER_NO, PLATE_NO);
        performEtagClose(ORDER_NO);

        DealerOrderCloseRequest request = buildCloseRequest(ORDER_NO, PLATE_NO);
        request.getSubscriptionInfo().setTotalAmt(26820);
        request.getSubscriptionInfo().setIsReturned(true);
        request.getSubscriptionInfo().setCloseAmt(34800);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/close", request);
        verifySuccessfulClose(result);

        eTagService.getETagIntoByOrderNo(ORDER_NO).forEach(response -> {
            assertEquals(ORDER_NO, response.getOrderNo());
            assertEquals(ETagFlow.RETURN_SUCCESS.getCode(), response.getETagFlow());
            assertEquals(ETagPayFlow.DONE.getCode(), response.getETagPayFlow());
            Assertions.assertNotNull(response.getDealerOrderPriceInfoId());
            assertTrue(response.isUploaded());
            Assertions.assertNotNull(response.getReturnDate());
            assertTrue(response.getDealerAndETagAccount());
            assertTrue(response.isLockETagAmt());
        });

        dealerOrderPriceInfoService.getDealerOrderPriceInfoByOrderNo(ORDER_NO).forEach(response -> {
            System.out.println(response);
            assertEquals(ORDER_NO, response.getOrderNo());
            Assertions.assertNotNull(response.getTradeId());
            assertEquals(TransactionItem.ETAG.getCode(), response.getTransactionItem());
        });

        DealerOrderResponse dealerOrderResponse = dealerOrderService.queryDealerOrderDetail(ORDER_NO);
        dealerOrderResponse.getDealerOrderPriceInfos().forEach(response -> {
            assertEquals(ORDER_NO, response.getOrderNo());
        });
        assertEquals(ID_NO, dealerOrderResponse.getCustomerInfo().getIdNo());
    }

    @Test
    public void cancelDealerOrder() throws Exception {
        DealerOrderQueryResponse testOrder = createCompleteTestOrder(CANCEL_ORDER_NO, BuIdEnum.subscribe);

        DealerOrderCancelRequest request = buildCancelRequestWithOrderNo(CANCEL_ORDER_NO, testOrder.getPlateNo(), "買到車子");
        ResultActions result = performPatch("/internal/subscribe/dealerOrder/cancel", request);

        verifySuccessfulCancel(result, "買到車子");
    }

    @Test
    public void cancelDealerOrder_OrderStatusIsNull() throws Exception {
        DealerOrderQueryResponse testOrder = createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        DealerOrderCancelRequest request = buildCancelRequestWithOrderNo(ORDER_NO, testOrder.getPlateNo(), "買到車子");
        updateRequestField(request, "orderStatus", null);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/cancel", request);
        verifySuccessfulCancel(result, "買到車子");
    }

    @Test
    public void cancelDealerOrder_Failure_OrderStatusIsGoing() throws Exception {
        DealerOrderQueryResponse testOrder = createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);
        performDepartOperation(ORDER_NO, testOrder.getPlateNo());

        DealerOrderCancelRequest request = buildCancelRequestWithOrderNo(ORDER_NO, testOrder.getPlateNo(), null);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/cancel", request);
        verifyBadRequestError(result, "訂單不為建立中狀態，不可取消");
    }

    @Test
    public void cancelDealerOrder_Success_CancelRemarkIsNull() throws Exception {
        DealerOrderQueryResponse testOrder = createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        DealerOrderCancelRequest request = buildCancelRequestWithOrderNo(ORDER_NO, testOrder.getPlateNo(), null);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/cancel", request);
        verifySuccessfulCancel(result, null);
    }

    @Test
    public void queryDealerOrder() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        Map<String, String> params = new HashMap<>();
        params.put("skip", "0");
        params.put("limit", "10");
        params.put("stationCode", EXPECT_DEPART_STATION);
        params.put("orderNo", ORDER_NO);

        ResultActions result = performGet("/internal/subscribe/dealerOrder/query", params);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.page.list[0].orderNo").value(ORDER_NO))
            .andExpect(jsonPath("$.data.page.list[0].customerInfo.idNo").value(ID_NO))
            .andExpect(jsonPath("$.data.page.list[0].expectDepartStation").doesNotHaveJsonPath())
            .andExpect(jsonPath("$.data.page.list[0].subscriptionInfo.expectReturnStation").exists());
    }

    @Test
    public void queryDealerOrder_Failure_WrongIdNo() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        Map<String, String> params = new HashMap<>();
        params.put("skip", "0");
        params.put("limit", "10");
        params.put("stationCode", EXPECT_DEPART_STATION);
        params.put("orderNo", ORDER_NO);
        params.put("idNo", "A123456789");

        ResultActions result = performGet("/internal/subscribe/dealerOrder/query", params);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.page.total").value(0))
            .andExpect(jsonPath("$.data.page.list").value(empty()));
    }

    @Test
    void queryDealerOrderDetail() throws Exception {
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        mockMvc.perform(get("/internal/subscribe/dealerOrder/{orderNo}", ORDER_NO))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderNo").value(ORDER_NO))
            .andExpect(jsonPath("$.data.customerInfo.idNo").value(ID_NO))
            .andExpect(jsonPath("$.data.expectDepartStation").doesNotHaveJsonPath())
            .andExpect(jsonPath("$.data.subscriptionInfo.expectReturnStation").exists());
    }

    @Test
    public void updateUserIdAndLrentalContractNo_Success_UpdateBoth() throws Exception {
        // 準備測試資料
        DealerOrderUpdateUserAndLrentalContractNoRequest request = new DealerOrderUpdateUserAndLrentalContractNoRequest();
        request.setOrderNo("24083132552");
        request.setDealerUserId(83L);
        request.setLrentalContractNo("110999995-W");

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/userAndLrentalContractNo", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.dealerUserId").value(83))
            .andExpect(jsonPath("$.data.lrentalContractNo").value("110999995-W"));
    }

    @Test
    public void updateUserIdAndLrentalContractNo_Success_ClearLrentalContractNo() throws Exception {
        // 準備測試資料
        DealerOrderUpdateUserAndLrentalContractNoRequest request = new DealerOrderUpdateUserAndLrentalContractNoRequest();
        request.setOrderNo("24083132552");
        request.setDealerUserId(83L);
        request.setLrentalContractNo(null);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/userAndLrentalContractNo", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.dealerUserId").value(83))
            .andExpect(jsonPath("$.data.lrentalContractNo").value(nullValue()));
    }

    @Test
    public void updateUserIdAndLrentalContractNo_Failure_DealerUserNotFound() throws Exception {
        // 準備測試資料
        DealerOrderUpdateUserAndLrentalContractNoRequest request = new DealerOrderUpdateUserAndLrentalContractNoRequest();
        request.setOrderNo("24083132552");
        request.setDealerUserId(999L);
        request.setLrentalContractNo("110999995-W");

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/userAndLrentalContractNo", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(DEALER_USER_NOT_FOUND.getCode()))
            .andExpect(jsonPath("$.message").value(containsString("找不到經銷商客戶")))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    public void updateUserIdAndLrentalContractNo_Failure_InvalidLrentalContract() throws Exception {
        // 準備測試資料
        DealerOrderUpdateUserAndLrentalContractNoRequest request = new DealerOrderUpdateUserAndLrentalContractNoRequest();
        request.setOrderNo("24083132552");
        request.setDealerUserId(83L);
        request.setLrentalContractNo("131299016-A");

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/userAndLrentalContractNo", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(LRENTAL_CONTRACT_NOT_FOUND.getCode()))
            .andExpect(jsonPath("$.message").value(containsString("找不到指定長租契約編號")))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    public void createDealerOrderWithSecondHandCarThenUpdateWithAnotherSecondHandCar() throws Exception {
        // 準備測試資料
        String orderNo = "************";
        DealerOrderCreateRequest dealerOrderCreateRequest = buildDealerOrderCreateRequest(orderNo, BuIdEnum.secondHand);

        performPost("/internal/subscribe/dealerOrder", dealerOrderCreateRequest)
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 自動撥車申請
        long currentTime = System.currentTimeMillis();
        mockMvc.perform(post("/internal/subscribe/v1/car/changeBu")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
                .header(CarPlusConstant.HEADER_COMPANY_CODE, "carplus")
                .content(String.format("{\"orderNo\":\"%s\",\"plateNo\":\"%s\",\"licenseExpDate\":%d,\"changeType\":\"AUTO_BATCH_CHANGE\"}",
                    orderNo, dealerOrderCreateRequest.getPlateNo(), currentTime)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 建立長租契約
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        LocalDateTime startDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(currentTime), ZoneId.systemDefault());
        LocalDateTime endDate = startDate.plusMonths(dealerOrderCreateRequest.getSubscriptionInfo().getSubscribeMonth());

        String memo = String.format("格上官網／新單／中古車\n%s：%s - %s（租期%d個月）\n",
            orderNo,
            startDate.format(formatter),
            endDate.format(formatter),
            dealerOrderCreateRequest.getSubscriptionInfo().getSubscribeMonth());
        memo = memo.replace("\n", "\\\\n");

        mockMvc.perform(post("/internal/subscribe/dealerOrder/addLrentalContract")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, HEADER_MEMBER_ID)
                .content(String.format("{\"orderNo\":\"%s\",\"licenseExpDate\":%d,\"replaceCodes\":[],\"memo\":\"%s\"}",
                    orderNo, currentTime, memo)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        DealerOrderUpdateRequest dealerOrderUpdateRequest = new DealerOrderUpdateRequest();
        dealerOrderUpdateRequest.setOrderNo(orderNo);
        dealerOrderUpdateRequest.setPlateNo(findAvailableCar(BuIdEnum.secondHand));
        dealerOrderUpdateRequest.setSubscriptionInfo(new DealerSubscriptionInfoForUpdate());

        performPatch("/internal/subscribe/dealerOrder", dealerOrderUpdateRequest)
            .andExpect(jsonPath("$.statusCode").value("0"));
    }

    // ======================== 站點驗證測試 ========================

    // ======================== 建立訂單的站點驗證測試 ========================

    @Test
    public void createDealerOrder_Success_WithValidStations() throws Exception {
        // 正常情況：有效的 expectDepartStation 和 expectReturnStation 
        DealerOrderCreateRequest request = buildDealerOrderCreateRequest("M202501010001", BuIdEnum.subscribe);

        ResultActions result = performPost("/internal/subscribe/dealerOrder", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.subscriptionInfo.expectDepartStation").value(EXPECT_DEPART_STATION))
            .andExpect(jsonPath("$.data.subscriptionInfo.expectReturnStation").value(EXPECT_RETURN_STATION));
    }

    @Test
    void createDealerOrder_Failure_EmptyExpectDepartStation() throws Exception {
        testStationValidation("expectDepartStation", "", "預定出車站點不可為空");
    }

    @Test
    void createDealerOrder_Failure_NullExpectDepartStation() throws Exception {
        testStationValidation("expectDepartStation", null, "預定出車站點不可為空");
    }

    @Test
    void createDealerOrder_Failure_EmptyExpectReturnStation() throws Exception {
        testStationValidation("expectReturnStation", "", "預定還車站點不可為空");
    }

    @Test
    void createDealerOrder_Failure_NullExpectReturnStation() throws Exception {
        testStationValidation("expectReturnStation", null, "預定還車站點不可為空");
    }

    @Test
    void createDealerOrder_Failure_InvalidExpectDepartStation() throws Exception {
        testStationValidation("expectDepartStation", "99999", "查無 99999 此站點編號");
    }

    @Test
    void createDealerOrder_Failure_InvalidExpectReturnStation() throws Exception {
        testStationValidation("expectReturnStation", "88888", "查無 88888 此站點編號");
    }

    @Test
    public void createDealerOrder_Failure_BothStationsInvalid() throws Exception {
        // 測試兩個站點都無效的情況
        DealerOrderCreateRequest request = buildDealerOrderCreateRequest("M202501010008", BuIdEnum.subscribe);
        request.getSubscriptionInfo().setExpectDepartStation("77777");
        request.getSubscriptionInfo().setExpectReturnStation("66666");

        ResultActions result = performPost("/internal/subscribe/dealerOrder", request);
        verifyBadRequestError(result, "查無 77777 此站點編號");
    }

    // ======================== 編輯訂單的站點驗證測試 ========================

    @Test
    public void updateDealerOrder_Success_WithValidStations() throws Exception {
        // 先建立一個訂單
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        // 正常情況：有效的 expectDepartStation 和 expectReturnStation
        DealerOrderUpdateRequest request = buildUpdateRequest(ORDER_NO, PLATE_NO);
        DealerSubscriptionInfoForUpdate subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation("71"); // 使用不同的有效站點
        subscriptionInfo.setExpectReturnStation("71"); // 使用不同的有效站點

        ResultActions result = performPatch("/internal/subscribe/dealerOrder", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));
    }

    @Test
    public void updateDealerOrder_Success_WithNullStations() throws Exception {
        // 先建立一個訂單
        String testOrderNo = "M202501020001";
        createCompleteTestOrder(testOrderNo, BuIdEnum.subscribe);

        // 測試站點為 null 的情況（應該成功，不會覆蓋原有值）
        DealerOrderUpdateRequest request = buildUpdateRequest(testOrderNo, PLATE_NO);
        DealerSubscriptionInfoForUpdate subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation(null);
        subscriptionInfo.setExpectReturnStation(null);

        ResultActions result = performPatch("/internal/subscribe/dealerOrder", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證原有站點資訊沒有被清空
        DealerOrderResponse orderDetail = dealerOrderService.queryDealerOrderDetail(testOrderNo);
        assertEquals(EXPECT_DEPART_STATION, orderDetail.getSubscriptionInfo().getExpectDepartStation());
        assertEquals(EXPECT_RETURN_STATION, orderDetail.getSubscriptionInfo().getExpectReturnStation());
    }

    @Test
    public void updateDealerOrder_Success_WithEmptyStations() throws Exception {
        // 先建立一個訂單
        String testOrderNo = "M202501020002";
        createCompleteTestOrder(testOrderNo, BuIdEnum.subscribe);

        // 測試站點為空字串的情況（應該成功，不會覆蓋原有值）
        DealerOrderUpdateRequest request = buildUpdateRequest(testOrderNo, PLATE_NO);
        DealerSubscriptionInfoForUpdate subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation("");
        subscriptionInfo.setExpectReturnStation("");

        ResultActions result = performPatch("/internal/subscribe/dealerOrder", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證原有站點資訊沒有被清空
        DealerOrderResponse orderDetail = dealerOrderService.queryDealerOrderDetail(testOrderNo);
        assertEquals(EXPECT_DEPART_STATION, orderDetail.getSubscriptionInfo().getExpectDepartStation());
        assertEquals(EXPECT_RETURN_STATION, orderDetail.getSubscriptionInfo().getExpectReturnStation());
    }

    @Test
    public void updateDealerOrder_Failure_InvalidExpectDepartStation() throws Exception {
        // 先建立一個訂單
        String testOrderNo = "M202501020003";
        createCompleteTestOrder(testOrderNo, BuIdEnum.subscribe);

        // 測試無效的 expectDepartStation
        DealerOrderUpdateRequest request = buildUpdateRequest(testOrderNo, PLATE_NO);
        request.getSubscriptionInfo().setExpectDepartStation("99999");

        ResultActions result = performPatch("/internal/subscribe/dealerOrder", request);
        verifyBadRequestError(result, "查無 99999 此站點編號");
    }

    @Test
    public void updateDealerOrder_Failure_InvalidExpectReturnStation() throws Exception {
        // 先建立一個訂單
        String testOrderNo = "M202501020004";
        createCompleteTestOrder(testOrderNo, BuIdEnum.subscribe);

        // 測試無效的 expectReturnStation
        DealerOrderUpdateRequest request = buildUpdateRequest(testOrderNo, PLATE_NO);
        request.getSubscriptionInfo().setExpectReturnStation("88888");

        ResultActions result = performPatch("/internal/subscribe/dealerOrder", request);
        verifyBadRequestError(result, "查無 88888 此站點編號");
    }

    @Test
    public void updateDealerOrder_Success_PartialStationUpdate() throws Exception {
        // 先建立一個訂單
        String testOrderNo = "M202501020005";
        createCompleteTestOrder(testOrderNo, BuIdEnum.subscribe);

        // 測試只更新其中一個站點
        DealerOrderUpdateRequest request = buildUpdateRequest(testOrderNo, PLATE_NO);
        DealerSubscriptionInfoForUpdate subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation("71"); // 只更新出車站點
        subscriptionInfo.setExpectReturnStation(null); // expectReturnStation 不設值（null）

        ResultActions result = performPatch("/internal/subscribe/dealerOrder", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證只有出車站點被更新，還車站點保持原值
        DealerOrderResponse orderDetail = dealerOrderService.queryDealerOrderDetail(testOrderNo);
        assertEquals("71", orderDetail.getSubscriptionInfo().getExpectDepartStation());
        assertEquals(EXPECT_RETURN_STATION, orderDetail.getSubscriptionInfo().getExpectReturnStation());
    }

    // ======================== 出車訂單的站點驗證測試 ========================

    @Test
    public void departDealerOrder_Success_WithValidStations() throws Exception {
        // 先建立一個訂單並建立長租契約
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        // 正常情況：有效的 expectDepartStation 和 expectReturnStation
        DealerOrderDepartRequest request = buildDepartRequest(ORDER_NO, PLATE_NO);
        DealerSubscriptionInfoForDepart subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation("71"); // 選填，有效站點
        subscriptionInfo.setExpectReturnStation("71"); // 選填，有效站點

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/depart", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.GOING.getCode()));
    }

    @Test
    public void departDealerOrder_Success_WithNullStations() throws Exception {
        // 先建立一個訂單並建立長租契約
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        // 測試站點為 null 的情況（應該成功，不會覆蓋原有值）
        DealerOrderDepartRequest request = buildDepartRequest(ORDER_NO, PLATE_NO);
        DealerSubscriptionInfoForDepart subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation(null); // 選填，null 值
        subscriptionInfo.setExpectReturnStation(null); // 選填，null 值

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/depart", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.GOING.getCode()));

        // 驗證原有站點資訊沒有被清空
        DealerOrderResponse orderDetail = dealerOrderService.queryDealerOrderDetail(ORDER_NO);
        assertEquals(EXPECT_DEPART_STATION, orderDetail.getSubscriptionInfo().getExpectDepartStation());
        assertEquals(EXPECT_RETURN_STATION, orderDetail.getSubscriptionInfo().getExpectReturnStation());
    }

    @Test
    public void departDealerOrder_Success_WithEmptyStations() throws Exception {
        // 先建立一個訂單並建立長租契約
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        // 測試站點為空字串的情況（應該成功，不會覆蓋原有值）
        DealerOrderDepartRequest request = buildDepartRequest(ORDER_NO, PLATE_NO);
        DealerSubscriptionInfoForDepart subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation(""); // 選填，空字串
        subscriptionInfo.setExpectReturnStation(""); // 選填，空字串

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/depart", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.GOING.getCode()));

        // 驗證原有站點資訊沒有被清空
        DealerOrderResponse orderDetail = dealerOrderService.queryDealerOrderDetail(ORDER_NO);
        assertEquals(EXPECT_DEPART_STATION, orderDetail.getSubscriptionInfo().getExpectDepartStation());
        assertEquals(EXPECT_RETURN_STATION, orderDetail.getSubscriptionInfo().getExpectReturnStation());
    }

    @Test
    public void departDealerOrder_Failure_InvalidExpectDepartStation() throws Exception {
        // 先建立一個訂單並建立長租契約
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        // 測試無效的 expectDepartStation
        DealerOrderDepartRequest request = buildDepartRequest(ORDER_NO, PLATE_NO);
        request.getSubscriptionInfo().setExpectDepartStation("99999"); // 無效站點

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/depart", request);
        verifyBadRequestError(result, "查無 99999 此站點編號");
    }

    @Test
    public void departDealerOrder_Failure_InvalidExpectReturnStation() throws Exception {
        // 先建立一個訂單並建立長租契約
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        // 測試無效的 expectReturnStation
        DealerOrderDepartRequest request = buildDepartRequest(ORDER_NO, PLATE_NO);
        request.getSubscriptionInfo().setExpectReturnStation("88888"); // 無效站點

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/depart", request);
        verifyBadRequestError(result, "查無 88888 此站點編號");
    }

    // ======================== 結案訂單的站點驗證測試 ========================

    @Test
    public void closeDealerOrder_Success_WithValidStations() throws Exception {
        // 先建立一個訂單、建立長租契約、出車、關閉 ETag
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);
        performDepartOperation(ORDER_NO, PLATE_NO);
        performEtagClose(ORDER_NO);

        // 正常情況：有效的 expectDepartStation 和 expectReturnStation
        DealerOrderCloseRequest request = buildCloseRequest(ORDER_NO, PLATE_NO);
        DealerSubscriptionInfoForClose subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation("71"); // 選填，有效站點
        subscriptionInfo.setExpectReturnStation("71"); // 選填，有效站點

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/close", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.COMPLETE.getCode()));
    }

    @Test
    public void closeDealerOrder_Success_WithNullStations() throws Exception {
        // 先建立一個訂單、建立長租契約、出車、關閉 ETag
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);
        performDepartOperation(ORDER_NO, PLATE_NO);
        performEtagClose(ORDER_NO);

        // 測試站點為 null 的情況（應該成功，不會覆蓋原有值）
        DealerOrderCloseRequest request = buildCloseRequest(ORDER_NO, PLATE_NO);
        DealerSubscriptionInfoForClose subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation(null); // 選填，null 值
        subscriptionInfo.setExpectReturnStation(null); // 選填，null 值

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/close", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.COMPLETE.getCode()));

        // 驗證原有站點資訊沒有被清空
        DealerOrderResponse orderDetail = dealerOrderService.queryDealerOrderDetail(ORDER_NO);
        assertEquals(EXPECT_DEPART_STATION, orderDetail.getSubscriptionInfo().getExpectDepartStation());
        assertEquals(EXPECT_RETURN_STATION, orderDetail.getSubscriptionInfo().getExpectReturnStation());
    }

    @Test
    public void closeDealerOrder_Success_WithEmptyStations() throws Exception {
        // 先建立一個訂單、建立長租契約、出車、關閉 ETag
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);
        performDepartOperation(ORDER_NO, PLATE_NO);
        performEtagClose(ORDER_NO);

        // 測試站點為空字串的情況（應該成功，不會覆蓋原有值）
        DealerOrderCloseRequest request = buildCloseRequest(ORDER_NO, PLATE_NO);
        DealerSubscriptionInfoForClose subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation(""); // 選填，空字串
        subscriptionInfo.setExpectReturnStation(""); // 選填，空字串

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/close", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.COMPLETE.getCode()));

        // 驗證原有站點資訊沒有被清空
        DealerOrderResponse orderDetail = dealerOrderService.queryDealerOrderDetail(ORDER_NO);
        assertEquals(EXPECT_DEPART_STATION, orderDetail.getSubscriptionInfo().getExpectDepartStation());
        assertEquals(EXPECT_RETURN_STATION, orderDetail.getSubscriptionInfo().getExpectReturnStation());
    }

    @Test
    public void closeDealerOrder_Failure_InvalidExpectDepartStation() throws Exception {
        // 先建立一個訂單、建立長租契約、出車、關閉 ETag
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);
        performDepartOperation(ORDER_NO, PLATE_NO);
        performEtagClose(ORDER_NO);

        // 測試無效的 expectDepartStation
        DealerOrderCloseRequest request = buildCloseRequest(ORDER_NO, PLATE_NO);
        request.getSubscriptionInfo().setExpectDepartStation("99999"); // 無效站點

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/close", request);
        verifyBadRequestError(result, "查無 99999 此站點編號");
    }

    @Test
    public void closeDealerOrder_Failure_InvalidExpectReturnStation() throws Exception {
        // 先建立一個訂單、建立長租契約、出車、關閉 ETag
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);
        performDepartOperation(ORDER_NO, PLATE_NO);
        performEtagClose(ORDER_NO);

        // 測試無效的 expectReturnStation
        DealerOrderCloseRequest request = buildCloseRequest(ORDER_NO, PLATE_NO);
        request.getSubscriptionInfo().setExpectReturnStation("88888"); // 無效站點

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/close", request);
        verifyBadRequestError(result, "查無 88888 此站點編號");
    }

    // ======================== 取消訂單的站點驗證測試 ========================

    @Test
    public void cancelDealerOrder_Success_WithValidStations() throws Exception {
        // 先建立一個訂單並建立長租契約
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        // 正常情況：有效的 expectDepartStation 和 expectReturnStation
        DealerOrderCancelRequest request = buildCancelRequest(ORDER_NO, "測試取消");
        DealerSubscriptionInfoForCancel subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation("71"); // 選填，有效站點
        subscriptionInfo.setExpectReturnStation("71"); // 選填，有效站點

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/cancel", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CANCEL.getCode()))
            .andExpect(jsonPath("$.data.isCancel").value(true));
    }

    @Test
    public void cancelDealerOrder_Success_WithNullStations() throws Exception {
        // 先建立一個訂單並建立長租契約
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        // 測試站點為 null 的情況（應該成功，不會覆蓋原有值）
        DealerOrderCancelRequest request = buildCancelRequest(ORDER_NO, "測試取消");
        DealerSubscriptionInfoForCancel subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation(null); // 選填，null 值
        subscriptionInfo.setExpectReturnStation(null); // 選填，null 值

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/cancel", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CANCEL.getCode()))
            .andExpect(jsonPath("$.data.isCancel").value(true));

        // 驗證原有站點資訊沒有被清空
        DealerOrderResponse orderDetail = dealerOrderService.queryDealerOrderDetail(ORDER_NO);
        assertEquals(EXPECT_DEPART_STATION, orderDetail.getSubscriptionInfo().getExpectDepartStation());
        assertEquals(EXPECT_RETURN_STATION, orderDetail.getSubscriptionInfo().getExpectReturnStation());
    }

    @Test
    public void cancelDealerOrder_Success_WithEmptyStations() throws Exception {
        // 先建立一個訂單並建立長租契約
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        // 測試站點為空字串的情況（應該成功，不會覆蓋原有值）
        DealerOrderCancelRequest request = buildCancelRequest(ORDER_NO, "測試取消");
        DealerSubscriptionInfoForCancel subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation(""); // 選填，空字串
        subscriptionInfo.setExpectReturnStation(""); // 選填，空字串

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/cancel", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CANCEL.getCode()))
            .andExpect(jsonPath("$.data.isCancel").value(true));

        // 驗證原有站點資訊沒有被清空
        DealerOrderResponse orderDetail = dealerOrderService.queryDealerOrderDetail(ORDER_NO);
        assertEquals(EXPECT_DEPART_STATION, orderDetail.getSubscriptionInfo().getExpectDepartStation());
        assertEquals(EXPECT_RETURN_STATION, orderDetail.getSubscriptionInfo().getExpectReturnStation());
    }

    @Test
    public void cancelDealerOrder_Failure_InvalidExpectDepartStation() throws Exception {
        // 先建立一個訂單並建立長租契約
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        // 測試無效的 expectDepartStation
        DealerOrderCancelRequest request = buildCancelRequest(ORDER_NO, "測試取消");
        request.getSubscriptionInfo().setExpectDepartStation("99999"); // 無效站點

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/cancel", request);
        verifyBadRequestError(result, "查無 99999 此站點編號");
    }

    @Test
    public void cancelDealerOrder_Failure_InvalidExpectReturnStation() throws Exception {
        // 先建立一個訂單並建立長租契約
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        // 測試無效的 expectReturnStation
        DealerOrderCancelRequest request = buildCancelRequest(ORDER_NO, "測試取消");
        request.getSubscriptionInfo().setExpectReturnStation("88888"); // 無效站點

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/cancel", request);
        verifyBadRequestError(result, "查無 88888 此站點編號");
    }

    @Test
    public void cancelDealerOrder_Success_BothStationsValidAndDifferent() throws Exception {
        // 先建立一個訂單並建立長租契約
        createCompleteTestOrder(ORDER_NO, BuIdEnum.subscribe);

        // 測試兩個不同的有效站點
        DealerOrderCancelRequest request = buildCancelRequest(ORDER_NO, "測試取消 - 兩個不同有效站點");
        DealerSubscriptionInfoForCancel subscriptionInfo = request.getSubscriptionInfo();
        subscriptionInfo.setExpectDepartStation("71"); // 有效站點1
        subscriptionInfo.setExpectReturnStation("88"); // 有效站點2

        ResultActions result = performPatch("/internal/subscribe/dealerOrder/cancel", request);
        result.andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.orderStatus").value(ContractStatus.CANCEL.getCode()))
            .andExpect(jsonPath("$.data.isCancel").value(true))
            .andExpect(jsonPath("$.data.cancelRemark").value("測試取消 - 兩個不同有效站點"));
    }
}