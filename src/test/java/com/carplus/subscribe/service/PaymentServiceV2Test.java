package com.carplus.subscribe.service;


import com.carplus.subscribe.db.mysql.dao.AccountDetailRepository;
import com.carplus.subscribe.db.mysql.dao.PaymentInfoRepository;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.payment.Account;
import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.enums.AccountType;
import com.carplus.subscribe.enums.Acquirer;
import com.carplus.subscribe.model.payment.req.PaymentRequest;
import com.carplus.subscribe.model.payment.resp.PaymentRes;
import com.carplus.subscribe.model.priceinfo.resp.OrderPriceInfoInternalResponse;
import com.carplus.subscribe.model.queue.PaymentQueue;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.MapUtils;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class PaymentServiceV2Test {

    @Autowired
    private OrderService orderService;

    @Autowired
    private PaymentServiceV2 paymentServiceV2;

    @Autowired
    private CheckoutService checkoutService;

    @Autowired
    private PaymentInfoRepository paymentInfoRepository;

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private PriceInfoService priceInfoService;

    @Autowired
    private AccountDetailRepository accountDetailRepository;

    @Test
    public void getAccountsByOrders() {
        accountDetailRepository.findLastDetailByAccount(Arrays.asList(403403L));

    }


    @Test
    public void getAccountsByOrder() throws JsonProcessingException {
        System.out.println(objectMapper.writeValueAsString(paymentServiceV2.getAccountsByOrder("M202310189176")));
    }

    @Test
    public void checkout() {
        Orders orders = orderService.getOrder("M202501109251");
        checkoutService.checkOut(orders);
    }

    @Test
    public void securityCheckOut() {
        Orders orders = orderService.getOrder("B11208290002");
//        PaymentInfo paymentInfo = paymentInfoRepository.getOne(196330);
        PaymentQueue queue = new PaymentQueue();
        queue.setAcquirer(Acquirer.TW_TAISHIN);
        queue.setAmount(10000);
        queue.setTransactionNumber("B11208290002P01");
//        BeanUtils.copyProperties(paymentInfo,queue);
        checkoutService.securityDepositCheckOut(orders, queue, false);
    }

    @Test
    public void orderCancelCheckOut() {
        Orders orders = orderService.getOrder("B11209130005");

        OrderPriceInfo cancelBooking = new OrderPriceInfo();
        cancelBooking.setAmount(0);
        cancelBooking.setOrderNo(orders.getOrderNo());
        cancelBooking.setType(2);

        checkoutService.orderRefundCheckOut(orders, cancelBooking);
    }


    @Test
    public void getPaymentsByOrder() {
        paymentServiceV2.getAccountsByOrder("M202403224501");
    }

    @Test
    @Transactional
    public void recordAccounts() throws JsonProcessingException {
        String json = "{\n" +
            "        \"stationCode\": \"201\",\n" +
            "        \"accountRecords\": [\n" +
            "            {\n" +
            "                \"createDate\": null,\n" +
            "                \"updateDate\": null,\n" +
            "                \"collectAuto\": null,\n" +
            "                \"totalRefundAmount\": 0,\n" +
            "                \"stationCode\": \"\",\n" +
            "                \"checkNo\": \"\",\n" +
            "                \"drawer\": \"\",\n" +
            "                \"checkAppointBack\": \"\",\n" +
            "                \"checkingAccNo\": \"\",\n" +
            "                \"checkDueDate\": null,\n" +
            "                \"transactionNumber\": \"\",\n" +
            "                \"payFrom\": \"Other\",\n" +
            "                \"accountType\": \"Remit\",\n" +
            "                \"amount\": 0,\n" +
            "                \"authCode\": \"\",\n" +
            "                \"cardNumber\": \"\",\n" +
            "                \"chargeType\": null,\n" +
            "                \"id\": null,\n" +
            "                \"originalAmount\": 99090,\n" +
            "                \"payFor\": \"Return\",\n" +
            "                \"remitAccCode\": \"***********\",\n" +
            "                \"remitNo\": 256768,\n" +
            "                \"remitter\": \"\",\n" +
            "                \"tradeId\": \"\",\n" +
            "                \"isDeleted\": false,\n" +
            "                \"refundAmount\": 0,\n" +
            "                \"orderPriceInfoIds\": [76049, 76050, 76051, 76052, 76720]\n" +
            "            }\n" +
            "        ]\n" +
            "    }";

        PaymentRequest paymentRequest = objectMapper.readValue(json, PaymentRequest.class);
        PaymentRes paymentRes = paymentServiceV2.recordAccounts(paymentRequest, "M202403216494");
        paymentServiceV2.payAndDiscountBalance("M202403216494");

        List<OrderPriceInfo> infos = priceInfoService.getPriceInfosByOrder("M202403216494");
        infos.stream().filter(info -> Arrays.asList(76049, 76050, 76051, 76052, 76720).contains(info.getId()))
            .forEach(info -> Assertions.assertNotEquals(null, info.getRemitAccountIds().get(0)));

        List<OrderPriceInfoInternalResponse> unPaidPriceInfoByOrderResponse = priceInfoService.getUnPaidPriceInfoByOrderResponse("M202403216494", true, false);
        unPaidPriceInfoByOrderResponse.stream().filter(info -> Arrays.asList(76049, 76050, 76051, 76052, 76720).contains(info.getId()))
            .forEach(info -> Assertions.assertEquals("PAID", info.getPayStatus().name()));
        unPaidPriceInfoByOrderResponse.stream().filter(info -> Objects.equals(76053, info.getId()))
            .forEach(info -> {
                Assertions.assertEquals("UNPAID", info.getPayStatus().name());
                Assertions.assertNull(info.getRemitAccountIds());
            });
    }

    @Test
    @Transactional
    public void recordAccounts_includingRefundOrderPriceInfo() throws JsonProcessingException {
        String json = "{\n" +
            "    \"stationCode\": \"201\",\n" +
            "    \"accountRecords\": [\n" +
            "      {\n" +
            "        \"createDate\": *************,\n" +
            "        \"updateDate\": *************,\n" +
            "        \"id\": 403473,\n" +
            "        \"collectAuto\": null,\n" +
            "        \"amount\": 0,\n" +
            "        \"totalRefundAmount\": 20400,\n" +
            "        \"accountType\": \"Credit\",\n" +
            "        \"payFor\": \"Depart\",\n" +
            "        \"stationCode\": \"233\",\n" +
            "        \"tradeId\": \"D20240710VhOnR8\",\n" +
            "        \"chargeType\": 15,\n" +
            "        \"cardNumber\": \"424242******4242\",\n" +
            "        \"authCode\": \"402949\",\n" +
            "        \"remitter\": null,\n" +
            "        \"remitAccCode\": null,\n" +
            "        \"remitNo\": null,\n" +
            "        \"checkNo\": null,\n" +
            "        \"drawer\": null,\n" +
            "        \"checkAppointBack\": null,\n" +
            "        \"checkingAccNo\": null,\n" +
            "        \"checkDueDate\": null,\n" +
            "        \"transactionNumber\": \"M202407100625P02\",\n" +
            "        \"refundAmount\": 0,\n" +
            "        \"orderPriceInfoIds\": null,\n" +
            "        \"isDeleted\": false,\n" +
            "        \"originalAmount\": 20400\n" +
            "      },\n" +
            "      {\n" +
            "        \"createDate\": *************,\n" +
            "        \"updateDate\": *************,\n" +
            "        \"id\": 403474,\n" +
            "        \"collectAuto\": null,\n" +
            "        \"amount\": 4000,\n" +
            "        \"totalRefundAmount\": 58400,\n" +
            "        \"accountType\": \"Remit\",\n" +
            "        \"payFor\": \"Depart\",\n" +
            "        \"stationCode\": \"\",\n" +
            "        \"tradeId\": \"\",\n" +
            "        \"chargeType\": null,\n" +
            "        \"cardNumber\": \"\",\n" +
            "        \"authCode\": \"\",\n" +
            "        \"remitter\": \"\",\n" +
            "        \"remitAccCode\": \"***********\",\n" +
            "        \"remitNo\": 254463,\n" +
            "        \"checkNo\": \"\",\n" +
            "        \"drawer\": \"\",\n" +
            "        \"checkAppointBack\": \"\",\n" +
            "        \"checkingAccNo\": \"\",\n" +
            "        \"checkDueDate\": null,\n" +
            "        \"transactionNumber\": \"\",\n" +
            "        \"refundAmount\": 0,\n" +
            "        \"orderPriceInfoIds\": [\n" +
            "          76691,\n" +
            "          76697\n" +
            "        ],\n" +
            "        \"isDeleted\": true,\n" +
            "        \"originalAmount\": 62400\n" +
            "      },\n" +
            "      {\n" +
            "        \"collectAuto\": null,\n" +
            "        \"amount\": 4000,\n" +
            "        \"totalRefundAmount\": 0,\n" +
            "        \"accountType\": \"Remit\",\n" +
            "        \"payFor\": \"Depart\",\n" +
            "        \"stationCode\": \"\",\n" +
            "        \"tradeId\": \"\",\n" +
            "        \"chargeType\": null,\n" +
            "        \"cardNumber\": \"\",\n" +
            "        \"authCode\": \"\",\n" +
            "        \"remitter\": \"\",\n" +
            "        \"remitAccCode\": \"***********\",\n" +
            "        \"remitNo\": 254463,\n" +
            "        \"checkNo\": \"\",\n" +
            "        \"drawer\": \"\",\n" +
            "        \"checkAppointBack\": \"\",\n" +
            "        \"checkingAccNo\": \"\",\n" +
            "        \"checkDueDate\": null,\n" +
            "        \"transactionNumber\": \"\",\n" +
            "        \"refundAmount\": 0,\n" +
            "        \"orderPriceInfoIds\": [\n" +
            "          76691,\n" +
            "          76697\n" +
            "        ],\n" +
            "        \"isDeleted\": false,\n" +
            "        \"originalAmount\": 4000\n" +
            "      }\n" +
            "    ]\n" +
            "  }";

        String orderNo = "M202407100625";

        PaymentRequest paymentRequest = objectMapper.readValue(json, PaymentRequest.class);
        PaymentRes paymentRes = paymentServiceV2.recordAccounts(paymentRequest, orderNo);
        paymentServiceV2.payAndDiscountBalance(orderNo);

        Optional<Account> deletedAccount = paymentServiceV2.getAccountsByOrders(orderNo).stream().filter(Account::isDeleted).findFirst();
        Optional<Account> newAccount = paymentRes.getAccounts().stream().filter(account -> account.getAccountType() == AccountType.Remit && MapUtils.isNotEmpty(account.getOrderPriceAmounts())).findFirst();

        List<OrderPriceInfo> infos = priceInfoService.getPriceInfosByOrder(orderNo);
        infos.stream().filter(info -> Arrays.asList(76691, 76697).contains(info.getId()))
            .forEach(info -> {
                Assertions.assertNotEquals(null, info.getRemitAccountIds().get(0));
                deletedAccount.ifPresent(account -> Assertions.assertNotEquals(account.getId(), info.getRemitAccountIds().get(0)));
                newAccount.ifPresent(account -> Assertions.assertEquals(account.getId(), info.getRemitAccountIds().get(0)));
            });

        List<OrderPriceInfoInternalResponse> unPaidPriceInfoByOrderResponse = priceInfoService.getUnPaidPriceInfoByOrderResponse(orderNo, true, false);
        unPaidPriceInfoByOrderResponse.stream().filter(info -> Arrays.asList(76691, 76697).contains(info.getId()))
            .forEach(info -> {
                Assertions.assertEquals("PAID", info.getPayStatus().name());
                Assertions.assertNotEquals(0, info.getReceivedAmount());
            });
    }

    @Test
    @Transactional
    public void testGetAccountsByOrder() {
        paymentServiceV2.getAccountsByOrder("M202408056881");
    }

    @Test
    public void getUnReconciliationPayments(){
        List<PaymentInfo> paymentInfoList = checkoutService.getUnReconciliationPayments("M202501143873");
        System.out.println(paymentInfoList);
    }
}
