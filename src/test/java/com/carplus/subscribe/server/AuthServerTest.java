package com.carplus.subscribe.server;

import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.audit.BlackListQueryRes;
import com.carplus.subscribe.model.auth.AuthDealerUser;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.auth.req.AuthDealerUserSaveRequest;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserResponse;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserUnionQuery;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.BACK_LIST;
import static org.junit.Assert.*;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class AuthServerTest {
    @Autowired
    private AuthServer authServer;

    @Test
    public void getDealerUsers(){
        AuthDealerUserUnionQuery query = new AuthDealerUserUnionQuery();
        query.setIdNo(Collections.singletonList("E190746076"));
        List<AuthDealerUserResponse> list = authServer.getDealerUsers(query);
        System.out.println(list.size());
    }

    @Test
    public void saveDealerUsers(){
        AuthDealerUserSaveRequest request = new AuthDealerUserSaveRequest(new ArrayList<>());
        AuthDealerUser user = new AuthDealerUser();
        user.setIdNo("11111");
        user.setUserName("劉帥哥");
        user.setNationalCode("886");
        user.setMainCell("0912345678");
        user.setBirthDay("1992/07/07");
        user.setEmail("<EMAIL>");
        user.setHhcityId(1);
        user.setHhareaId(1);
        user.setHhaddress("測試路");
        request.getUsers().add(user);
        List<AuthDealerUserResponse> list = authServer.saveDealerUsers(request);
        System.out.println(list.size());
    }

    @Test
    public void approveAuditPreSignedReview(){
        authServer.approveAuditPreSignedReview(1000214,"K2456");
    }

    /**
     * 測試批次帳號黑名單檢查
     */
    @Test
    public void testCheckAcctBlackList() {
        // 準備測試資料
        List<Integer> acctIds = Arrays.asList(
            ********, // 測試帳號1
            ********, // 測試帳號2
            ********  // 測試帳號3
        );

        // 執行批次黑名單檢查
        List<Integer> blacklistedAccounts = authServer.checkAcctBlackList(acctIds);

        // 顯示結果
        System.out.println("檢查帳號數量: " + acctIds.size());
        System.out.println("黑名單帳號數量: " + blacklistedAccounts.size());
        if (!blacklistedAccounts.isEmpty()) {
            System.out.println("黑名單帳號: " + blacklistedAccounts);
        } else {
            System.out.println("所有帳號都不在黑名單中");
        }

        // 驗證結果
        assertNotNull("黑名單結果不應為 null", blacklistedAccounts);
    }

    /**
     * 測試批次身分證黑名單檢查
     */
    @Test
    public void testCheckPidBlackList() {
        // 準備測試資料
        List<String> idNos = Arrays.asList(
            "A123456789",
            "B987654321",
            "C111222333"
        );

        // 執行批次黑名單檢查
        List<String> blacklistedPids = authServer.checkPidBlackList(idNos);

        // 顯示結果
        System.out.println("檢查身分證數量: " + idNos.size());
        System.out.println("黑名單身分證數量: " + blacklistedPids.size());
        if (!blacklistedPids.isEmpty()) {
            System.out.println("黑名單身分證: " + blacklistedPids);
        } else {
            System.out.println("所有身分證都不在黑名單中");
        }

        // 驗證結果
        assertNotNull("黑名單結果不應為 null", blacklistedPids);
    }

    /**
     * 測試空列表的黑名單檢查
     */
    @Test
    public void testCheckBlackListWithEmptyList() {
        // 測試空的帳號列表
        List<Integer> emptyAcctIds = Collections.emptyList();
        List<Integer> blacklistedAccounts = authServer.checkAcctBlackList(emptyAcctIds);

        assertTrue("空列表應返回空結果", blacklistedAccounts.isEmpty());

        // 測試空的身分證列表
        List<String> emptyIdNos = Collections.emptyList();
        List<String> blacklistedPids = authServer.checkPidBlackList(emptyIdNos);

        assertTrue("空列表應返回空結果", blacklistedPids.isEmpty());
    }

    /**
     * 測試大量資料的批次黑名單檢查 (測試分批功能)
     */
    @Test
    public void testCheckBlackListWithLargeData() {
        // 建立超過批次大小(10)的測試資料
        List<Integer> largeAcctIds = new ArrayList<>();
        for (int i = 1; i <= 25; i++) {
            largeAcctIds.add(******** + i);
        }

        long startTime = System.currentTimeMillis();

        // 執行批次黑名單檢查
        List<Integer> blacklistedAccounts = authServer.checkAcctBlackList(largeAcctIds);

        long endTime = System.currentTimeMillis();

        // 顯示結果
        System.out.println("檢查帳號數量: " + largeAcctIds.size());
        System.out.println("黑名單帳號數量: " + blacklistedAccounts.size());
        System.out.println("執行時間: " + (endTime - startTime) + " ms");
        System.out.println("預期批次數: " + ((largeAcctIds.size() + 9) / 10));

        // 驗證結果
        assertNotNull("黑名單結果不應為 null", blacklistedAccounts);
    }

    /**
     * 測試併發查詢相同帳號 (測試去重功能)
     */
    @Test
    public void testConcurrentGetUserWithRetry() throws InterruptedException {
        int acctId = 1000214;
        int threadCount = 5;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threadCount);
        List<CompletableFuture<AuthUser>> futures = new ArrayList<>();

        // 建立多個執行緒同時查詢相同帳號
        for (int i = 0; i < threadCount; i++) {
            CompletableFuture<AuthUser> future = CompletableFuture.supplyAsync(() -> {
                try {
                    startLatch.await(); // 等待所有執行緒準備好
                    return authServer.getUserWithRetry(acctId);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                } finally {
                    endLatch.countDown();
                }
            });
            futures.add(future);
        }

        // 同時啟動所有執行緒
        long startTime = System.currentTimeMillis();
        startLatch.countDown();

        // 等待所有執行緒完成
        endLatch.await(10, TimeUnit.SECONDS);
        long endTime = System.currentTimeMillis();

        // 收集結果
        List<AuthUser> results = futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());

        // 顯示結果
        System.out.println("併發查詢數: " + threadCount);
        System.out.println("執行時間: " + (endTime - startTime) + " ms");
        System.out.println("結果數量: " + results.size());

        // 驗證所有結果應該相同 (因為查詢相同帳號)
        if (!results.isEmpty()) {
            AuthUser firstUser = results.get(0);
            for (AuthUser user : results) {
                assertEquals("所有查詢應返回相同結果", firstUser.getAcctId(), user.getAcctId());
            }
            System.out.println("所有查詢返回相同結果，去重功能正常");
        }
    }

    @Test
    public void testVerifyAccountStatusExceptionPattern() {
        // 測試停權帳號
        SubscribeException subscribeException = assertThrows(SubscribeException.class, () -> authServer.verifyAccountStatus(389154));
        assertEquals(BACK_LIST.getCode(), subscribeException.getCode().getCode());
        String subscribeExceptionReasonPattern = Pattern.compile(".*" + BlackListQueryRes.Fields.inBlackList + ".*" + BlackListQueryRes.Fields.subSuspended + ".*").pattern();
        assertNotNull(subscribeException.getReason());
        assertTrue(subscribeException.getReason().matches(subscribeExceptionReasonPattern));
    }
}
