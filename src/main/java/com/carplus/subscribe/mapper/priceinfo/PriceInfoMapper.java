package com.carplus.subscribe.mapper.priceinfo;

import carplus.common.response.exception.BadRequestException;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.model.AccidentInfo;
import com.carplus.subscribe.model.priceinfo.CalculateStage;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import com.carplus.subscribe.model.priceinfo.req.InternalAccidentRequest;
import com.carplus.subscribe.model.request.priceinfo.ExtraFeeRequest;
import com.carplus.subscribe.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.carplus.subscribe.enums.OrderStatus.DEPART;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.CarAccident;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.Insurance;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Pay;

@Slf4j
public class PriceInfoMapper {

    private PriceInfoMapper() {}

    /**
     * 更新車損費用 OrderPriceInfo
     */
    public static void buildUpdatedCarAccidentOPI(ExtraFeeRequest request, ExtraFeeRequest.ExtraFee fee, Orders order, OrderPriceInfo opi, String memberId) {
        opi.setCategory(fee.getCategory());
        opi.setAmount(fee.getAmount());

        // 確保 infoDetail 存在，然後設定屬性
        if (opi.getInfoDetail() == null) {
            opi.setInfoDetail(new PriceInfoDetail());
        }

        PriceInfoDetail detail = opi.getInfoDetail();
        detail.setReason(fee.getReason());
        detail.setCarLossAmt(fee.getAmount());
        detail.setARCarLossAmt(fee.getARCarLossAmt());
        // infoDetail.adminId 保持不變，除非不存在，才設定為 memberId
        if (detail.getAdminId() == null) {
            detail.setAdminId(memberId);
        }
        detail.setChargingPoint(fee.getPoint());

        // 更新 Order AccidentInfo (AccidentInfo.adminId 維持不變，除非不存在，才設定為 memberId)
        AccidentInfo info = toCarAccidentTypeInfo(request, Optional.ofNullable(detail.getAdminId()).orElse(memberId));
        order.setAccidentInfo(info);
    }

    /**
     * 更新額外費用 OrderPriceInfo
     */
    public static void buildUpdatedExtraFeeOPI(ExtraFeeRequest.ExtraFee fee, OrderPriceInfo opi, String memberId) {
        opi.setCategory(fee.getCategory());
        opi.setAmount(fee.getAmount());

        // 確保 infoDetail 存在，然後設定值
        if (opi.getInfoDetail() == null) {
            opi.setInfoDetail(new PriceInfoDetail());
        }

        opi.getInfoDetail().setReason(fee.getReason());
        // infoDetail.adminId 保持不變，除非不存在，才設定為 memberId
        if (opi.getInfoDetail().getAdminId() == null) {
            opi.getInfoDetail().setAdminId(memberId);
        }
        opi.getInfoDetail().setChargingPoint(fee.getPoint());
    }

    /**
     * OrderPriceInfo Category 車損費用
     */
    public static OrderPriceInfo buildNewCarAccidentOPI(ExtraFeeRequest.ExtraFee fee, Orders order, CalculateStage targetStage, OrderPriceInfo opi, String memberId) {
        if (order.getStatus() < DEPART.getStatus()) {
            throw new BadRequestException("需出車後才可設定車損");
        }

        // 設定 OPI 基本屬性
        opi.setStage(fee.getStage());
        opi.setLastPayDate(DateUtil.convertToEndOfInstant(targetStage.getEndDate()));
        opi.setCategory(fee.getCategory());
        opi.setType(Pay.getCode());
        opi.setAmount(fee.getAmount());
        opi.setOrderNo(order.getOrderNo());

        // 建立並設定 PriceInfoDetail
        PriceInfoDetail pid = createBasicPriceInfoDetail(fee, targetStage, memberId);
        pid.setCarLossAmt(fee.getAmount());
        pid.setARCarLossAmt(fee.getARCarLossAmt());

        opi.setInfoDetail(pid);
        return opi;
    }

    /**
     * OrderPriceInfo Category 額外費用
     */
    public static OrderPriceInfo buildNewExtraFeeOPI(ExtraFeeRequest.ExtraFee fee, Orders order, CalculateStage targetStage, OrderPriceInfo opi, String memberId) {
        // 設定 OPI 基本屬性
        opi.setStage(fee.getStage());
        opi.setLastPayDate(DateUtil.convertToEndOfInstant(targetStage.getEndDate()));
        opi.setCategory(fee.getCategory());
        opi.setType(Pay.getCode());
        opi.setAmount(fee.getAmount());
        opi.setOrderNo(order.getOrderNo());

        // 建立並設定 PriceInfoDetail
        PriceInfoDetail pid = createBasicPriceInfoDetail(fee, targetStage, memberId);
        if (Insurance.equals(fee.getCategory())) {
            pid.setInsurance(fee.getAmount() / pid.getMonth());
        }

        opi.setInfoDetail(pid);
        return opi;
    }

    private static PriceInfoDetail createBasicPriceInfoDetail(ExtraFeeRequest.ExtraFee fee, CalculateStage targetStage, String memberId) {
        PriceInfoDetail pid = new PriceInfoDetail();
        pid.setReason(fee.getReason());
        pid.setAdminId(memberId);
        pid.setChargingPoint(fee.getPoint());
        pid.setDay(targetStage.getDay());
        pid.setMonth(targetStage.getMonth());
        return pid;
    }

    public static AccidentInfo toCarAccidentTypeInfo(final ExtraFeeRequest req, final String memberId) {
        return AccidentInfo.builder()
                .carDamaged(req.isCarDamaged())
                .returnNego(req.getReturnNego())
                .adminId(memberId)
                .build();
    }

    public static ExtraFeeRequest toExtraFeeReq(InternalAccidentRequest accidentRequest, int latestStage) {
        ExtraFeeRequest.ExtraFee extraFee = ExtraFeeRequest.ExtraFee.builder()
                .category(CarAccident)
                .stage(latestStage)
                .amount(accidentRequest.getCarLossAmt())
                .aRCarLossAmt(accidentRequest.getARCarLossAmt())
                .reason(accidentRequest.getRemark())
                .build();
        // Mobile 事故還車 如果金額為0，則不新增一筆 0 的費用 (後台則反之，FE會強制送 0元 費用 payload建立)
        List<ExtraFeeRequest.ExtraFee> extraFees = new ArrayList<>();
        if (accidentRequest.getARCarLossAmt() != 0 && accidentRequest.getCarLossAmt() != 0) {
            extraFees.add(extraFee);
        }
        return ExtraFeeRequest.builder()
                .returnNego(accidentRequest.getReturnNego())
                .carDamaged(accidentRequest.isCarDamaged())
                .extraFeeList(extraFees)
                .build();
    }
}
