package com.carplus.subscribe.controller.payment;

import carplus.common.model.PageRequest;
import carplus.common.redis.cache.Lock;
import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.payment.Account;
import com.carplus.subscribe.model.payment.req.AccountOrderPriceBindingRequest;
import com.carplus.subscribe.model.payment.req.AccountSettlementRequest;
import com.carplus.subscribe.model.payment.req.ManualRefundSecurityDepositRequest;
import com.carplus.subscribe.model.payment.req.PaymentRequest;
import com.carplus.subscribe.model.payment.resp.PaymentRes;
import com.carplus.subscribe.model.payment.resp.SecurityDepositInfoResponse;
import com.carplus.subscribe.model.priceinfo.req.OrderPriceInfoRefundRequest;
import com.carplus.subscribe.model.priceinfo.req.OrderPriceInfoRefundRetryRequest;
import com.carplus.subscribe.model.priceinfo.resp.CancelOrderCalculateResponse;
import com.carplus.subscribe.model.queue.PaymentQueue;
import com.carplus.subscribe.model.request.paymentinfo.PaymentInfoQueryRequest;
import com.carplus.subscribe.model.request.paymentinfo.PaymentInfoRemarkRequest;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.response.paymentinfo.PaymentInfoQueryResponse;
import com.carplus.subscribe.rabbitmq.PaymentListener;
import com.carplus.subscribe.service.CheckoutService;
import com.carplus.subscribe.service.OrderService;
import com.carplus.subscribe.service.PaymentServiceV2;
import com.carplus.subscribe.service.PriceInfoService;
import com.carplus.subscribe.utils.CsvUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Slf4j
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 訂閱帳務收/支 API")
@CarPlusRestController
@Validated
public class InternalPaymentV1Controller {

    @Autowired
    private PaymentServiceV2 paymentService;

    @Autowired
    private CheckoutService checkoutService;

    @Autowired
    private PriceInfoService priceInfoService;

    @Autowired
    private PaymentListener paymentListener;
    @Autowired
    private OrderService orderService;

    @Operation(summary = "取得訂單帳目")
    @GetMapping(value = "/subscribe/v1/payment/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Account> getPayments(@PathVariable("orderNo") String orderNo) {
        return paymentService.getAccountsByOrder(orderNo);
    }

    @Operation(summary = "帳目登打")
    @PostMapping(value = "/subscribe/v1/payment/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public PaymentRes paymentsRecord(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                     @PathVariable("orderNo") String orderNo,
                                     @Validated @RequestBody PaymentRequest paymentRequest) {
        PaymentRes resp = paymentService.recordAccounts(paymentRequest, orderNo);
        resp.getOrders().setETagInfos(null);
        paymentService.checkTappayRefundSuccess(orderNo, paymentRequest.getAccountRecords());
        return resp;
    }

    @Operation(summary = "登打與帳務是否收支平衡")
    @GetMapping(value = "/subscribe/v1/payment/{orderNo}/balance", produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean isBalance(@PathVariable("orderNo") String orderNo) {
        return paymentService.checkBalance(orderNo);
    }

    @Operation(summary = "退款")
    @PostMapping("subscribe/v1/payment/refund")
    public void refundOrderPriceInfo(@Validated @RequestBody OrderPriceInfoRefundRequest orderPriceInfoRefundRequest) throws JsonProcessingException {
        paymentService.refundOrderPriceInfos(orderPriceInfoRefundRequest);
    }

    @Operation(summary = "退款失敗重新嘗試退款")
    @PostMapping("subscribe/v1/payment/refund/retry")
    public void refundOrderPriceInfoRetry(@Validated @RequestBody OrderPriceInfoRefundRetryRequest orderPriceInfoRefundRequest) throws JsonProcessingException {
        paymentService.refundOrderPriceInfosRetry(orderPriceInfoRefundRequest);
    }

    @Operation(summary = "訂單取消試算")
    @GetMapping(value = "/subscribe/v1/payment/{orderNo}/cancel", produces = MediaType.APPLICATION_JSON_VALUE)
    public CancelOrderCalculateResponse setAccidentPriceInfo(
        @PathVariable("orderNo") String orderNo) {
        return priceInfoService.cancelOrderCalculate(orderNo);
    }

    @Operation(summary = "系統台帳務作業列表")
    @GetMapping(value = "subscribe/v1/payment/paymentInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<PaymentInfoQueryResponse> queryPaymentInfo(
        PaymentInfoQueryRequest queryRequest) {
        return PageResponse.of(paymentService.searchByPage(new PageRequest(queryRequest.getLimit(), queryRequest.getSkip()), queryRequest));
    }

    @Operation(summary = "系統台帳務作業異動帳務備註")
    @PatchMapping(value = "subscribe/v1/payment/{paymentId}/remark", produces = MediaType.APPLICATION_JSON_VALUE)
    public PaymentInfoQueryResponse updatePaymentRemark(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
        @PathVariable("paymentId") int paymentId,
        @RequestBody PaymentInfoRemarkRequest request) {
        return paymentService.updatePaymentRemark(paymentId, request.getRemark(), memberId);
    }

    @Operation(summary = "系統台帳務作業列表CSV下載")
    @GetMapping(value = "subscribe/v1/payment/paymentInfo/csv", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public byte[] queryPaymentInfoCsv(
        HttpServletResponse res,
        PaymentInfoQueryRequest queryRequest) {
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = paymentService.getPaymentInfoCsv(queryRequest);
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        res.setHeader("Content-Disposition", "attachment; filename=SUB_payment_{" + sf.format(new Date()) + "}.csv");
        return ByteBuffer
            .allocate(out.size())
            .put(out.toByteBuffer())
            .array();
    }

    @Operation(summary = "系統台帳務作業保證金明細")
    @GetMapping(value = "subscribe/v1/payment/{orderNo}/securityDeposit", produces = MediaType.APPLICATION_JSON_VALUE)
    public SecurityDepositInfoResponse securityDeposit(@PathVariable("orderNo") String orderNo) {
        return paymentService.getSecurityDepositInfo(orderNo);
    }

    @Operation(summary = "更新申請日期表示完成人工退款")
    @PatchMapping(value = "subscribe/v1/payment/{orderNo}/securityDeposit", produces = MediaType.APPLICATION_JSON_VALUE)
    public void securityDeposit(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String headerMemberId,
        @RequestBody @Validated ManualRefundSecurityDepositRequest request,
        @PathVariable("orderNo") String orderNo) {

        paymentService.updateApplyDate(orderNo, new Date(request.getApplyDate().toEpochMilli()), request.getManualRefundStatus(), request.getRefundMethod(), headerMemberId);
    }

    @Operation(summary = "收支登打並開立/作廢/折讓發票")
    @PatchMapping(value = "subscribe/v1/payment/{orderNo}/accountSettlement", produces = MediaType.APPLICATION_JSON_VALUE)
    @Lock(group = CheckoutService.class, key = "#orderNo", ttl = 60 * 2)
    public void accountSettlement(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                  @PathVariable("orderNo") String orderNo,
                                  @Validated @RequestBody AccountSettlementRequest request) {
        paymentService.accountSettlement(request, orderNo, memberId);
        paymentService.payAndDiscountBalance(orderNo);
        checkoutService.checkOut(orderNo);
    }

    @Operation(summary = "將付款與帳務勾稽 - 後台作業用")
    @PatchMapping(value = "subscribe/v1/payment/{orderNo}/binding", produces = MediaType.APPLICATION_JSON_VALUE)
    public void orderPriceBindingRequest(
        @PathVariable("orderNo") String orderNo,
        @Validated @RequestBody AccountOrderPriceBindingRequest request) {
        paymentService.bindPriceInfo(orderNo, request);
    }

    @Operation(summary = "訂單立帳")
    @GetMapping(value = "subscribe/v1/payment/{orderNo}/checkout", produces = MediaType.APPLICATION_JSON_VALUE)
    public void orderPriceBindingRequest(
        @PathVariable("orderNo") String orderNo) {
        checkoutService.checkOut(orderNo);
    }

    @Operation(summary = "保證金人工退款立帳")
    @GetMapping(value = "subscribe/v1/payment/{orderNo}/manualRefundSecurityDepositCheckOut", produces = MediaType.APPLICATION_JSON_VALUE)
    public void manualRefundSecurityDepositCheckOut(
        @PathVariable("orderNo") String orderNo) {
        checkoutService.manualRefundSecurityDepositCheckOut(orderNo);
    }

    @Operation(summary = "接收Queue")
    @PostMapping("subscribe/v1/payment/queue")
    public void receiveQueue(@RequestBody PaymentQueue queue) {
        paymentListener.subscribeReceive(queue, 0, null);
    }

    @Operation(summary = "重新立暫收款")
    @PostMapping("subscribe/v1/payment/advance/queue")
    public void receiveAdvanceQueue(@RequestBody PaymentQueue queue) {
        Orders order = orderService.getOrder(queue.getOrderId());
        checkoutService.advance(queue, order);
    }

}
