package com.carplus.subscribe.controller.contract;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusCode;
import carplus.common.response.CarPlusRestController;
import carplus.common.response.Result;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.model.order.MainContractResponse;
import com.carplus.subscribe.model.payment.req.PayAuthRequest;
import com.carplus.subscribe.model.payment.resp.PayAuthResponse;
import com.carplus.subscribe.model.priceinfo.req.UpdateMileagePriceInfoRequest;
import com.carplus.subscribe.model.request.contract.ContractCreateReq;
import com.carplus.subscribe.model.request.contract.ContractCriteria;
import com.carplus.subscribe.model.request.contract.OrderRenewRequest;
import com.carplus.subscribe.model.request.order.OrderRenewStatusRequest;
import com.carplus.subscribe.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CarPlusRestController
@Tag(name = "訂閱合約API")
public class ContractPublicController {

    @Autowired
    private ContractLogic contractLogic;

    @Autowired
    private PriceInfoService priceInfoService;

    @Autowired
    private ContractService contractService;

    @Autowired
    private PaymentServiceV2 paymentService;

    @Autowired
    private OrderService orderService;

    @Operation(summary = "建立合約")
    @PostMapping("subscribe/contract")
    public Contract createContract(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId, @Validated @RequestBody ContractCreateReq req) {
        contractLogic.contractCreateRequestValidate(req, false);
        return contractLogic.createContract(req, acctId, null, true, null);
    }

    @Operation(summary = "續約")
    @PostMapping("subscribe/{orderNo}/renew")
    public Orders renewContract(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @Validated @RequestBody OrderRenewRequest req,
        @PathVariable("orderNo") String orderNo) {
        req.setAcctId(acctId);
        return contractLogic.renewOrder(orderNo, req, false, null, true, null);
    }

    @Operation(summary = "是否可續約")
    @PostMapping("subscribe/{mainContract}/renewable")
    public Boolean renewContract(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @PathVariable("mainContract") String mainContract) {
        return contractLogic.isRenewable(mainContract, acctId);
    }

    @Operation(summary = "保證金付款")
    @PostMapping(value = "/subscribe/{mainContractNo}/payForSecurityDeposit", produces = MediaType.APPLICATION_JSON_VALUE)
    public PayAuthResponse payForSecurityDeposit(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @RequestBody @Validated PayAuthRequest payAuthRequest,
        @PathVariable("mainContractNo") String mainContractNo) {
        return paymentService.paySecurityDeposit(payAuthRequest, mainContractNo, acctId);
    }


    @Operation(summary = "使用者主約需付款資訊清單")
    @GetMapping(value = "/subscribe/mainContract/{mainContractNo}/priceInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfo> contractPriceInfo(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @PathVariable("mainContractNo") String mainContractNo) {
        return priceInfoService.getUserUnPaidPriceInfo(acctId, mainContractNo);
    }

    @Operation(summary = "一般付款")
    @PostMapping(value = "/subscribe/{orderNo}/pay", produces = MediaType.APPLICATION_JSON_VALUE)
    public PayAuthResponse payFor(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @RequestBody @Validated PayAuthRequest payAuthRequest,
        @PathVariable("orderNo") String orderNo) {
        return paymentService.pay(payAuthRequest, orderNo, acctId);
    }

    @Operation(summary = "設定里程費")
    @PatchMapping(value = "/subscribe/{orderNo}/mileageAmt", produces = MediaType.APPLICATION_JSON_VALUE)
    public OrderPriceInfo setMileage(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @RequestBody @Validated UpdateMileagePriceInfoRequest updateMileagePriceInfoRequest,
        @PathVariable("orderNo") String orderNo) {
        priceInfoService.calculateMileageFeeValidate(orderNo, acctId, updateMileagePriceInfoRequest.getOrderPriceInfoId());
        OrderPriceInfo orderPriceInfo = priceInfoService.calculateMileageFee(orderNo, acctId, updateMileagePriceInfoRequest.getCurrentMileage(), updateMileagePriceInfoRequest.getOrderPriceInfoId());
        return priceInfoService.get(orderPriceInfo.getId());
    }

    @Operation(summary = "進行中的訂單數量")
    @GetMapping(value = "/subscribe/order/process/count", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result processOrdersCount(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId) {
        return Result.of(orderService.getUserProcessOrdersCount(acctId), CarPlusCode.SUCCESS, null);
    }

    @Operation(summary = "拿取主約")
    @GetMapping(value = "/subscribe/mainContract", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<MainContractResponse> getMainContractOrders(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
                                                            @RequestParam(value = "skip", defaultValue = "0", required = false) Integer skip,
                                                            @RequestParam(value = "limit", defaultValue = "10", required = false) Integer limit,
                                                            @Validated ContractCriteria contractCriteria) {
        contractCriteria.setAcctId(acctId);
        return contractService.commonSearchPage(new PageRequest(limit, skip), contractCriteria);
    }

    @Operation(summary = "拿取訂單")
    @GetMapping("subscribe/order/{orderNo}")
    public Orders findOrders(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
                             @PathVariable("orderNo") String orderNo) {
        return orderService.getUserOrder(orderNo, acctId);
    }

    @Operation(summary = "續約狀態")
    @PatchMapping(value = "subscribe/{orderNo}/renewStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    public void renewStatus(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @PathVariable("orderNo") String orderNo,
        @RequestBody @Validated OrderRenewStatusRequest orderRenewStatusRequest) {
        orderService.updateRenewStatus(orderNo, acctId, orderRenewStatusRequest);
    }

    @Operation(summary = "3D驗證取消付款，放車")
    @PatchMapping(value = "subscribe/{orderNo}/releaseCar", produces = MediaType.APPLICATION_JSON_VALUE)
    public void releaseCarByOrder(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @PathVariable("orderNo") String orderNo) {
        orderService.releaseCar(orderNo, acctId);
    }
}