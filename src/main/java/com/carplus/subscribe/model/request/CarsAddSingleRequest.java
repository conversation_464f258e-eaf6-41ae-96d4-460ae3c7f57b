package com.carplus.subscribe.model.request;

import com.carplus.subscribe.db.mysql.entity.cars.CarPropertyProvider;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.utils.CarsUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.ArrayList;
import java.util.List;

@Data
public class CarsAddSingleRequest implements CarPropertyProvider {

    @Schema(description = "車號 (RAA 開頭且長度為 7 碼為虛擬車)")
    @NotBlank(message = "車號不可為空")
    private String plateNo;
    @Schema(description = "最新里程數")
    @NotNull(message = "最新里程數不可為空")
    @PositiveOrZero(message = "最新里程數不可小於 0")
    private Integer currentMileage;
    @Schema(description = "車型編號")
    @NotBlank(message = "車型編號不可為空")
    private String carModelCode;
    @Schema(description = "訂閱類別, NEW:新車; OLD:中古車", example = "OLD")
    @NotNull(message = "訂閱類別不可為空")
    private CarDefine.CarState carState;
    @Schema(description = "訂閱車方案")
    @NotNull(message = "訂閱車方案不可為空")
    @Positive(message = "訂閱車方案須為正整數")
    private Integer subscribeLevel;
    @Schema(description = "上下架狀態 (是否在訂閱官網上架)")
    @NotNull(message = "上下架狀態不可為空")
    private CarDefine.Launched launched;
    @Schema(description = "是否開放經銷商上架")
    @NotNull(message = "是否開放經銷商上架不可為空")
    private Boolean isSealandLaunched;
    @Schema(description = "當前所在站所代碼")
    private String locationStationCode;
    @Schema(description = "標籤")
    private List<Integer> tagIds;
    @Schema(description = "配備")
    private List<Integer> equipIds;
    @Schema(description = "排檔類別")
    private CarDefine.GearType gearType;
    @Schema(description = "車色")
    private String colorDesc;
    @Schema(description = "車體介紹")
    private String cnDesc;
    @Schema(description = "燃料種類")
    private CarDefine.FuelType fuelType;
    @Schema(description = "車型類別")
    private CarDefine.CarType carType;
    @Schema(description = "能源類別")
    private CarDefine.EnergyType energyType;
    @Schema(description = "車籍統編")
    @NotBlank(message = "車籍統編不可為空")
    private String vatNo;
    @Schema(description = "排氣量")
    private Integer displacement;
    @Schema(description = "座位數")
    private Integer seat;
    @Schema(description = "出廠年份")
    private String mfgYear;
    @Schema(description = "出廠月份")
    private String mfgMonth;
    @Min(value = 1, message = "準備工作天數不可小於 1")
    @Max(value = 127, message = "準備工作天數不可大於 127")
    @Schema(description = "準備工作天數")
    private Integer prepWorkdays;
    @Schema(description = "牌價")
    private Integer stdPrice;

    public void validate() {
        List<String> errors = new ArrayList<>();

        if (!CarsUtil.isCarPlusCar(vatNo)) {
            if (displacement == null) {
                errors.add("排氣量不可為空");
            }
            if (seat == null) {
                errors.add("座位數不可為空");
            }
            if (mfgYear == null) {
                errors.add("出廠年份不可為空");
            }
        }

        if (plateNo.startsWith("RAA")) {
            if (isVirtualCar()) {
                if (energyType == null) {
                    errors.add("能源類別不可為空");
                }
                if (displacement == null) {
                    errors.add("排氣量不可為空");
                }
                if (seat == null) {
                    errors.add("座位數不可為空");
                }
                if (mfgYear == null) {
                    errors.add("出廠年份不可為空");
                }
                if (stdPrice == null) {
                    errors.add("牌價不可為空");
                }
                if (locationStationCode == null) {
                    errors.add("當前所在站所代碼不可為空");
                }
                if (!CarsUtil.isCarPlusCar(vatNo)) {
                    errors.add("虛擬車僅能為格上車籍");
                }
            } else {
                errors.add("車號格式錯誤, 虛擬車車號開頭須為 RAA 且長度為 7 碼");
            }
        }

        if (!errors.isEmpty()) {
            String errorMessage = String.join("; ", errors);
            throw new IllegalArgumentException(errorMessage);
        }
    }
}
