package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.redis.cache.Lock;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.CarWishlistRepository;
import com.carplus.subscribe.db.mysql.dto.CarBrandModelDTO;
import com.carplus.subscribe.db.mysql.entity.CarWishlist;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.crs.CarBase;
import com.carplus.subscribe.model.crs.CarBaseEnum;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistCriteria;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistReportRequest;
import com.carplus.subscribe.model.request.carwishlist.CommonCarWishlistCriteria;
import com.carplus.subscribe.model.response.carwishlist.CarWishlistReportData;
import com.carplus.subscribe.model.response.carwishlist.CarWishlistResponse;
import com.carplus.subscribe.model.response.carwishlist.CarWishlistResponseWithSnapshot;
import com.carplus.subscribe.model.response.order.OrderQueryResponse;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.utils.CsvUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CarWishlistService {

    private final CarWishlistRepository carWishlistRepository;
    private final CarsService carsService;
    private final AuthServer authServer;
    private final SubscribeLevelService subscribeLevelService;
    private final StationService stationService;
    private final CrsService crsService;
    private final OrderService orderService;

    public void add(Integer acctId, String plateNo) {

        AuthUser authUser = authServer.getUserWithRetry(acctId);

        Cars car = Optional.ofNullable(carsService.findByPlateNo(plateNo))
            .filter(Cars::isOrderableOfficially)
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.CAR_NOT_FOUND));

        // 檢查是否存在未刪除的記錄
        if (carWishlistRepository.existsByAcctIdAndPlateNo(acctId, plateNo)) {
            throw new SubscribeException(SubscribeHttpExceptionCode.CAR_ALREADY_IN_WISHLIST);
        }

        SubscribeLevel subscribeLevel = subscribeLevelService.determineSubscribeLevel(car);

        // 檢查是否存在軟刪除的記錄
        Optional<CarWishlist> existingWishlist = carWishlistRepository.findByAcctIdAndPlateNo(acctId, plateNo, true);
        CarWishlist carWishlist;

        if (existingWishlist.isPresent()) {
            // 恢復軟刪除的記錄
            carWishlist = existingWishlist.get();
            carWishlist.setUseMonthlyFee(car.getUseMonthlyFee(subscribeLevel));
            carWishlist.setMileageFee(subscribeLevel.getMileageFee());
            carWishlist.setSecurityDeposit(subscribeLevel.getSecurityDeposit());
            carWishlist.setType(subscribeLevel.getType());
            carWishlist.setTagIds(car.getTagIds());
            carWishlist.setLocationStationCode(car.getLocationStationCode());
            carWishlist.setDeletedAt(null);
        } else {
            // 檢查收藏清單數量限制
            long wishlistCount = carWishlistRepository.countByAcctIdAndDeletedAtIsNull(acctId);
            if (wishlistCount >= CarPlusConstant.CAR_WISHLIST_LIMIT_PER_USER) {
                throw new SubscribeException(SubscribeHttpExceptionCode.CAR_WISHLIST_LIMIT_EXCEEDED);
            }

            // 創建新記錄
            carWishlist = new CarWishlist(authUser.getAcctId(), car, subscribeLevel);
        }

        carWishlistRepository.save(carWishlist);
    }

    public void remove(Integer acctId, String plateNo) {

        CarWishlist carWishlist = carWishlistRepository.findByAcctIdAndPlateNo(acctId, plateNo, false)
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.CAR_NOT_IN_WISHLIST));

        carWishlist.setDeletedAt(Instant.now());
        carWishlistRepository.save(carWishlist);
    }

    public <T extends CommonCarWishlistCriteria> Page<? extends CarWishlistResponse> searchByPage(T criteria) {
        int skip = criteria.getSkip();
        int limit = criteria.getLimit();

        long count = carWishlistRepository.count(criteria);
        if (count == 0) {
            return Page.of(0, Collections.emptyList(), skip, limit);
        }

        List<? extends CarWishlistResponse> list = searchPage(criteria, skip, limit);
        return Page.of(count, list, skip, limit);
    }

    private <T extends CommonCarWishlistCriteria> List<? extends CarWishlistResponse> searchPage(T criteria, int offset, int limit) {

        Map<String, Stations> stationMap = stationService.findAll().stream()
            .collect(Collectors.toMap(Stations::getStationCode, Function.identity()));

        List<CarWishlist> carWishlists = carWishlistRepository.findBySearch(criteria, offset, limit);
        Map<String, CarWishlist> carWishlistMap = carWishlists.stream()
            .collect(Collectors.toMap(CarWishlist::getPlateNo, Function.identity()));
        List<String> plateNos = carWishlists.stream()
            .map(CarWishlist::getPlateNo)
            .collect(Collectors.toList());

        // 根據 criteria 類型獲取 brandCode 和 carModelCode
        List<String> brandCodes = (criteria instanceof CarWishlistCriteria) ? ((CarWishlistCriteria) criteria).getBrandCodes() : null;
        List<String> carModelCodes = (criteria instanceof CarWishlistCriteria) ? ((CarWishlistCriteria) criteria).getCarModelCodes() : null;
        Map<String, CarResponse> carInfoMap = carsService.getCarInfoList(plateNos, brandCodes, carModelCodes).stream()
            .collect(Collectors.toMap(CarResponse::getPlateNo, Function.identity()));

        return plateNos.stream()
            .map(plateNo -> {
                CarWishlist carWishlist = carWishlistMap.get(plateNo);
                CarResponse carInfo = carInfoMap.get(plateNo);
                if (carInfo == null) {
                    return null;
                }
                if (criteria instanceof CarWishlistCriteria) {
                    return new CarWishlistResponseWithSnapshot(carWishlist, carInfo, stationMap);
                }
                return new CarWishlistResponse(carWishlist, carInfo, stationMap.get(carInfo.getLocationStationCode()));
            })
            .filter(Objects::nonNull) // 過濾掉可能的 null 值
            .collect(Collectors.toList());
    }

    public boolean isCarInWishlist(Integer acctId, String plateNo) {
        return carWishlistRepository.existsByAcctIdAndPlateNo(acctId, plateNo);
    }

    public List<CarWishlistReportData> generateWishlistReportData(CarWishlistReportRequest request) {
        List<CarWishlist> wishlists;
        Map<Integer, AuthUser> authUserMap;

        // 根據手機號碼篩選客戶或取得所有收藏清單
        if (StringUtils.isNotBlank(request.getPhone())) {
            List<AuthUser> authUsers = authServer.getUserAcctIds(null, request.getPhone(), null);
            if (authUsers.isEmpty()) {
                return Collections.emptyList();
            }

            // 提取 acctId
            List<Integer> acctIds = authUsers.stream()
                .map(AuthUser::getAcctId)
                .collect(Collectors.toList());

            authUserMap = authUsers.stream()
                .collect(Collectors.toMap(AuthUser::getAcctId, Function.identity()));

            // 根據 acctId 和其他條件篩選收藏清單
            wishlists = carWishlistRepository.findByReportCriteria(request, acctIds);
        } else {
            // 根據條件取得所有收藏清單 (不篩選手機號碼)
            wishlists = carWishlistRepository.findByReportCriteria(request);

            if (wishlists.isEmpty()) {
                return Collections.emptyList();
            }

            authUserMap = authServer.getUserAcctIds(wishlists.stream()
                .map(CarWishlist::getAcctId).distinct().toArray(Integer[]::new)).stream()
                .collect(Collectors.toMap(AuthUser::getAcctId, Function.identity()));
        }

        if (wishlists.isEmpty()) {
            return Collections.emptyList();
        }

        // 建立報表資料
        return buildReportData(wishlists, authUserMap);
    }
    
    private List<CarWishlistReportData> buildReportData(List<CarWishlist> wishlists, Map<Integer, AuthUser> authUserMap) {
        // 取得所有車牌號碼用於查詢車輛資訊
        List<String> plateNos = wishlists.stream()
            .map(CarWishlist::getPlateNo)
            .collect(Collectors.toList());
        
        // 取得車輛廠牌和車型資訊
        Map<String, CarBrandModelDTO> carBrandModelMap = carsService.getCarBrandModelDTOByCars(plateNos)
            .stream()
            .collect(Collectors.toMap(dto -> dto.getCar().getPlateNo(), Function.identity()));

        // 計算每位客戶的願望清單總數
        Set<Integer> acctIds = wishlists.stream()
            .map(CarWishlist::getAcctId)
            .collect(Collectors.toSet());
        Map<Integer, Long> wishlistCountMap = acctIds.stream()
            .collect(Collectors.toMap(Function.identity(), carWishlistRepository::countByAcctIdAndDeletedAtIsNull));

        // 取得所有車輛的實際採用訂閱方案名稱
        List<Cars> cars = carBrandModelMap.values().stream()
            .map(CarBrandModelDTO::getCar)
            .collect(Collectors.toList());
        Map<String, String> subscribeLevelNameMap = subscribeLevelService.getSubscribeLevelNamesIn(cars);

        // 取得所有車輛的 CRS 車輛資訊
        Map<String, CarBaseInfoSearchResponse> carBaseInfoMap = crsService.getCars(plateNos);

        // 取得所有車輛的進行中訂單資訊
        Map<String, List<OrderQueryResponse>> processingOrdersMap = orderService.getProcessOrdersByPlateNosMap(plateNos);
        
        // Build report data
        return wishlists.stream()
            .map(wishlist -> {
                AuthUser authUser = authUserMap.get(wishlist.getAcctId());
                CarBrandModelDTO carBrandModelDTO = carBrandModelMap.get(wishlist.getPlateNo());
                
                Cars car = carBrandModelDTO.getCar();
                
                CarWishlistReportData data = new CarWishlistReportData();
                
                // 客戶資訊
                data.setCustomerName(authUser.getCustomerName());
                data.setPhoneNumber(authUser.getMainCell());
                data.setTotalWishlistCount(wishlistCountMap.get(wishlist.getAcctId()));
                
                // 收藏清單資訊
                data.setWishlistAddDate(new Date(wishlist.getInstantUpdateDate().toEpochMilli()));
                data.setPlateNo(wishlist.getPlateNo());
                
                // 車輛資訊
                data.setBrandName(Optional.ofNullable(carBrandModelDTO.getBrand()).map(CarBrand::getBrandName).orElse(null));
                data.setCarModelName(Optional.ofNullable(carBrandModelDTO.getModel()).map(CarModel::getCarModelName).orElse(null));
                data.setCarState(car.getCarState().getName());
                data.setMfgYear(car.getMfgYear());
                // 訂閱方案名稱
                data.setSubscribeLevelName(subscribeLevelNameMap.get(wishlist.getPlateNo()));
                // 決定車輛庫存狀態：
                // 1. 取得 CRS 車輛資訊
                // 2. 提取車輛狀態代碼
                // 3. 若狀態不是「新車」則查詢對應的狀態名稱
                // 4. 若狀態是「新車」、null、或查無對應狀態，則使用車輛的上架狀態名稱作為預設值
                data.setInventoryStatus(
                    Optional.ofNullable(carBaseInfoMap.get(wishlist.getPlateNo()))
                        .map(CarBaseInfoSearchResponse::getCarBase)
                        .map(CarBase::getCarStatus)
                        .filter(status -> !CarBaseEnum.CarStatus.newCar.getCode().equals(status))
                        .flatMap(status -> Optional.ofNullable(CarBaseEnum.CarStatus.ofEnum(status)))
                        .map(CarBaseEnum.CarStatus::getName)
                        .orElse(car.getLaunched().getName())
                );

                // 收藏清單快照資料
                data.setMonthlyFeeAtWishlist(wishlist.getUseMonthlyFee());
                data.setMileageRateAtWishlist(wishlist.getMileageFee());
                data.setSecurityDepositAtWishlist(wishlist.getSecurityDeposit());

                // 檢查車輛狀態是否為 已訂閱/營業出車 且 收藏人是否為該車進行中訂單的訂車人
                data.setIsSubscribed(
                    Arrays.asList(CarDefine.CarStatus.Subscribed.getCode(), CarDefine.CarStatus.BizOut.getCode()).contains(car.getCarStatus())
                        && Optional.ofNullable(processingOrdersMap.get(wishlist.getPlateNo()))
                        .filter(CollectionUtils::isNotEmpty)
                        .map(orders -> orders.stream().anyMatch(order -> wishlist.getAcctId() == order.getAcctId()))
                        .orElse(false)
                        ? "是" : "否"
                );
                
                return data;
            })
            .collect(Collectors.toList());
    }

    @Lock(group = CarWishlistService.class, key = "'wishlist_report'", ttl = 60 * 2, errorMsg = "生成車輛收藏清單報表中，請稍候再試")
    public CsvUtil.ByteArrayOutputStream2ByteBuffer generateWishlistReportCsv(CarWishlistReportRequest request) {
        // 產生報表資料
        List<CarWishlistReportData> reportData = generateWishlistReportData(request);
        
        // 產生匯出 CSV
        return createCsvOutput(reportData);
    }
    
    private CsvUtil.ByteArrayOutputStream2ByteBuffer createCsvOutput(List<CarWishlistReportData> reportData) {
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CsvUtil.createCsv(
            reportData,
            CarWishlistReportData.HEADS,
            true,
            ',',
            out,
            Charset.forName("big5"),
            CarWishlistReportData.class
        );
        return out;
    }
}
