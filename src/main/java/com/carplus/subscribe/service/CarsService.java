package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.redis.cache.Get;
import carplus.common.response.CarPlusCode;
import carplus.common.response.exception.BadRequestException;
import carplus.common.response.exception.LogicException;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.CarsRepository;
import com.carplus.subscribe.db.mysql.dto.CarBrandModelDTO;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.db.mysql.entity.cars.*;
import com.carplus.subscribe.db.mysql.entity.change.EntityChangeLog;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.cars.ProcessingOrder;
import com.carplus.subscribe.model.cars.req.*;
import com.carplus.subscribe.model.cars.resp.*;
import com.carplus.subscribe.model.crs.*;
import com.carplus.subscribe.model.insurance.RequisitionCreateRequest;
import com.carplus.subscribe.model.insurance.SubscribeInsurance;
import com.carplus.subscribe.model.inventory.GeoInfo;
import com.carplus.subscribe.model.inventory.resp.InventoryResponse;
import com.carplus.subscribe.model.order.Remark;
import com.carplus.subscribe.model.request.CarModelQueryRequest;
import com.carplus.subscribe.model.request.CarsAddRequest;
import com.carplus.subscribe.model.request.CarsAddSingleRequest;
import com.carplus.subscribe.model.request.CarsUpdateRequest;
import com.carplus.subscribe.model.request.campaign.CarsCondition;
import com.carplus.subscribe.model.request.carregistration.*;
import com.carplus.subscribe.model.request.carwishlist.CommonCarWishlistCriteria;
import com.carplus.subscribe.model.request.dealer.DealerOrderCriteria;
import com.carplus.subscribe.model.response.campaign.CampaignCommonDetailResponse;
import com.carplus.subscribe.model.response.campaign.CampaignInternalDetailResponse;
import com.carplus.subscribe.model.response.carwishlist.CarWishlistResponse;
import com.carplus.subscribe.model.response.dealer.DealerOrderQueryResponse;
import com.carplus.subscribe.model.response.order.OrderQueryResponse;
import com.carplus.subscribe.model.subscribelevel.SubscribeLevelResponse;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.server.cars.LrentalServer;
import com.carplus.subscribe.server.cars.model.CarHistorySearchRep;
import com.carplus.subscribe.server.cars.model.CarPropertyResp;
import com.carplus.subscribe.server.cars.model.Da41Entity;
import com.carplus.subscribe.service.helpers.AddCarBuilder;
import com.carplus.subscribe.service.helpers.AddCarBuilderDataPreSetup;
import com.carplus.subscribe.utils.CarsUtil;
import com.carplus.subscribe.utils.CsvUtil;
import com.carplus.subscribe.utils.ExcelUtil;
import com.carplus.subscribe.utils.ZipUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static com.carplus.subscribe.utils.CarsUtil.encodePlateNo;

@Slf4j
@Service
public class CarsService {
    @Autowired
    private CrsService crsService;
    @Autowired
    private CarTagService carTagService;
    @Autowired
    private CarEquipService carEquipService;
    @Autowired
    private CarModelService carModelService;
    @Autowired
    private StationService stationService;
    @Autowired
    private LrentalServer lrentalServer;
    @Autowired
    private SubscribeLevelService subscribeLevelService;
    @Autowired
    private CarRegistrationService carRegistrationService;
    @Autowired
    private CarsRepository carsRepository;
    @Autowired
    private EntityChangeLogService entityChangeLogService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private DealerOrderService dealerOrderService;
    @Autowired
    private BuChangeService buChangeService;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private ContractService contractService;
    @Autowired
    private AuthorityServer authorityServer;
    @Autowired
    private AddCarBuilder addCarBuilder;
    @Autowired
    private InsuranceService insuranceService;
    @Autowired
    private CampaignService campaignService;
    @Autowired
    private CarWishlistService carWishlistService;
    @Autowired
    private ObjectMapper objectMapper;

    @Value("${station.subscribe}")
    private String subscribeStationCode;

    @Value("${gcs.url}")
    private String gcsUrl;

    /**
     * Steps <br/>
     * 1. {@link AddCarBuilder#processingPreData(String, String, String, boolean)} <br/>
     * 2. {@link AddCarBuilder#processCarsAddRequest(CarsAddRequest, CarBaseInfoToAddResponse, boolean, Map, String, String, boolean)} <br/>
     * 3. {@link #checkCrsCarNoExistThenUpdate(List, boolean, List, String, MemberInfo)} <br/>
     * {@link CarsUtil#setAndValidateNonVirtualCarRequiredFields(Cars, CarsAddRequest)}
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void add(CarsAddRequest request, String memberId) {

        Cars car = findByPlateNo(request.getPlateNo());
        if (Objects.nonNull(car)) {
            throw new BadRequestException(String.format("[車籍已存在, 車號: {%s}]", request.getPlateNo()));
        }
        checkForMutuallyExclusiveDiscountTags(request.getTagIds());

        boolean isNotVirtualAndIsCarPlusCar = !request.isVirtualCar() && CarsUtil.isCarPlusCar(request.getVatNo());
        AddCarBuilderDataPreSetup dataPreSetup = addCarBuilder.processingPreData(request.getPlateNo(), memberId, request.getCarModelCode(), isNotVirtualAndIsCarPlusCar);
        car = addCarBuilder.processCarsAddRequest(request, dataPreSetup.getCarBaseInfoToAdd(),
            dataPreSetup.isCrsCarNoExist(), dataPreSetup.getLatestExistingCarsInfo(), subscribeStationCode, dataPreSetup.getCheckedCarModelCode(), dataPreSetup.isNotVirtualAndIsCarPlusCar());

        if (dataPreSetup.isNotVirtualAndIsCarPlusCar()) {
            List<String> orderNos = checkCrsCarNoExistThenUpdate(dataPreSetup.getCars(), dataPreSetup.isCrsCarNoExist(),
                dataPreSetup.getOrderNos(), request.getPlateNo(), dataPreSetup.getMemberInfo());

            // BuChangeLog
            if (!orderNos.isEmpty() && dataPreSetup.isCrsCarNoExist()) {
                for (String orderNo : orderNos) {
                    buChangeService.addBuChangeLogByCar(car, orderNo);
                }
            }
        }

        validateSubscribeAndDiscountLevelEligibility(car);

        carsRepository.save(car);
    }

    /**
     * 檢查車籍標籤是否同時有優惠月費和超激優惠
     *
     * @param tagIds 車籍標籤 ID
     */
    private void checkForMutuallyExclusiveDiscountTags(List<Integer> tagIds) {
        if (CollectionUtils.isNotEmpty(tagIds) && tagIds.contains(CarDefine.CarTag.MONTHLY_DISCOUNTED.getId()) && tagIds.contains(CarDefine.CarTag.LEVEL_DISCOUNTED.getId())) {
            throw new SubscribeException(TAG_IDS_CONTAIN_MONTHLY_DISCOUNTED_AND_LEVEL_DISCOUNTED);
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void update(String plateNo, CarsUpdateRequest request) {

        Cars entity = findByPlateNo(plateNo);
        if (Objects.isNull(entity)) {
            throw new BadRequestException(String.format("[車籍不存在, 車號: {%s}]", plateNo));
        }
        Integer level = request.getSubscribeLevel();
        if (level != null) {
            // 當要異動方案時，有狀態<=50的訂單，則不可異動
            List<OrderQueryResponse> bookingAndDepartOrders = orderService.getBookingAndDepartOrdersByPlateNos(Collections.singletonList(entity.getPlateNo()));
            if (!Objects.equals(entity.getSubscribeLevel(), level) && !bookingAndDepartOrders.isEmpty()) {
                throw new SubscribeException(SUBSCRIBE_LEVEL_CAN_NOT_CHANGE);
            }
        }
        validateCarTagUpdateEligibility(entity, request.getTagIds());

        // 如為null視為不修改
        BeanUtils.copyProperties(request, entity, carplus.common.utils.BeanUtils.ignorePropertyNames(request));
        entity.setBuChangeMasterId(request.getBuChangeMasterId());
        entity.setPlateNo(plateNo);
        entity.validateCarMfgYear();
        validateSubscribeAndDiscountLevelEligibility(entity);
        entity.setPrepWorkdays(request.getPrepWorkdays());

        Optional.ofNullable(request.getType()).ifPresent(entity::setCarType);
        Optional.ofNullable(request.getIsSealandLaunched()).ifPresent(entity::setSealandLaunched);
        String carModelCode = request.getCarModelCode();
        if (StringUtils.isNotBlank(carModelCode)) {
            entity.setCarModelCode(carModelService.checkCarModelCodeAndGet(carModelCode));
        }
        carsRepository.save(entity);
    }

    /**
     * 更新車籍資料
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void update(Cars cars) {
        carsRepository.save(cars);
    }

    private void validateCarTagUpdateEligibility(Cars car, List<Integer> tagIds) {
        List<Integer> carTagIds = car.getTagIds();

        // 處理 null 和 empty 的情況
        boolean carTagsEmpty = carTagIds == null || carTagIds.isEmpty();
        boolean requestTagsEmpty = tagIds == null || tagIds.isEmpty();

        // 如果兩者都是空的 (null 或 empty)，視為沒有異動
        if (carTagsEmpty && requestTagsEmpty) {
            return;
        }

        // 如果都不是空的，才使用 CollectionUtils.isEqualCollection 判斷是否真的有異動，沒有異動則直接返回
        if (!carTagsEmpty && !requestTagsEmpty && CollectionUtils.isEqualCollection(carTagIds, tagIds)) {
            return;
        }

        checkForMutuallyExclusiveDiscountTags(tagIds);

        boolean hasDiscountTag = CollectionUtils.isNotEmpty(tagIds)
            && (tagIds.contains(CarDefine.CarTag.MONTHLY_DISCOUNTED.getId()) || tagIds.contains(CarDefine.CarTag.LEVEL_DISCOUNTED.getId()));
        boolean isSubscribedOrBizOut = CarDefine.CarStatus.Subscribed.getCode().equals(car.getCarStatus()) || CarDefine.CarStatus.BizOut.getCode().equals(car.getCarStatus());

        if (hasDiscountTag && isSubscribedOrBizOut) {
            throw new SubscribeException(CANNOT_UPDATE_SUBSCRIBED_OR_BIZOUT_CAR_TAGS);
        }
    }

    /**
     * 驗證車輛應具備啟用超激優惠條件
     */
    private void validateSubscribeAndDiscountLevelEligibility(Cars car) {
        SubscribeLevel subscribeLevel = subscribeLevelService.findByLevel(car.getSubscribeLevel());
        if (car.hasLevelDiscountedTag()) {
            subscribeLevel.checkMissingCorrespondingDiscountLevel();
            subscribeLevelService.checkDiscountLevelExistence(subscribeLevel.getDiscountLevel());
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateIsDeleted(String plateNo, Integer paramDeleted) {
        boolean isDeleted = paramDeleted == 1;
        carsRepository.updateIsDeleted(plateNo, isDeleted);
    }

    @Transactional
    public Cars addCarFromCrsByPlateNo(String plateNo) {
        // 建立 RetryTemplate
        RetryTemplate retryTemplate = RetryTemplate.builder()
            .maxAttempts(3) // 最大重試次數(包含第一次)
            .fixedBackoff(5000) // 重試延遲時間(毫秒)
            .build();

        // 執行重試邏輯
        CarBaseInfoSearchResponse carBaseInfo = retryTemplate.execute(context -> {
            log.info("正在進行第 {} 次向 CRS 查詢車輛資訊, plateNo: {}",
                context.getRetryCount() + 1, plateNo);

            CarBaseInfoSearchResponse response = crsService.getCar(plateNo);
            if (response == null) {
                throw new SubscribeException(CRS_CAR_NOT_FOUND);
            }
            return response;
        });

        Cars cars = addCarByCrs(carBaseInfo);
        if (cars.getLaunched() == CarDefine.Launched.tbc) {
            notifyService.notifyAddCarFromCrs(cars.getPlateNo());
        }
        return cars;
    }

    /**
     * Steps <br/>
     * 1. {@link AddCarBuilder#processingPreData(String, String, String, boolean)}  <br/>
     * 2. {@link AddCarBuilder#processCrsResponseBaseInfo(CarBaseInfoSearchResponse, CarBaseInfoToAddResponse, boolean, Map, String, String)}  <br/>
     * 3. {@link #checkCrsCarNoExistThenUpdate(List, boolean, List, String, MemberInfo)} <br/>
     */
    @Transactional
    public Cars addCarByCrs(CarBaseInfoSearchResponse carBaseInfo) {
        Cars cars = findByPlateNo(carBaseInfo.getPlateNo());
        if (cars != null) {
            return cars;
        }

        AddCarBuilderDataPreSetup dataPreSetup = addCarBuilder.processingPreData(carBaseInfo.getPlateNo(), null, null, true);
        cars = addCarBuilder.processCrsResponseBaseInfo(carBaseInfo, null, dataPreSetup.isCrsCarNoExist(),
            dataPreSetup.getLatestExistingCarsInfo(), subscribeStationCode, null);
        List<String> orderNos = checkCrsCarNoExistThenUpdate(dataPreSetup.getCars(), dataPreSetup.isCrsCarNoExist(),
            dataPreSetup.getOrderNos(), carBaseInfo.getPlateNo(), null);

        Cars finalCars = cars;
        Optional.of(carBaseInfo).map(CarBaseInfoSearchResponse::getCarSpecInfoResponse).map(CarSpecInfoResponse::getCarPlusEnergyCode).ifPresent(energyCode -> finalCars.setEnergyType(CarDefine.EnergyType.of(energyCode)));

        if (!orderNos.isEmpty() && dataPreSetup.isCrsCarNoExist()) {
            orderNos.forEach(orderNo -> buChangeService.addBuChangeLogByCar(finalCars, orderNo));
        }

        carsRepository.save(cars);
        return cars;
    }

    /**
     * Steps <br/>
     * 1. {@link AddCarBuilder#processingPreData(String, String, String, boolean)}   <br/>
     * 2. {@link AddCarBuilder#processCarsAddSingleRequest(CarsAddSingleRequest, CarBaseInfoToAddResponse, boolean, Map, String, String)}  <br/>
     * 3. {@link #checkCrsCarNoExistThenUpdate(List, boolean, List, String, MemberInfo)} <br/>
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void addSingle(CarsAddSingleRequest request, String memberId) {
        String newPlateNo = request.getPlateNo();
        checkCarExistence(newPlateNo);
        checkForMutuallyExclusiveDiscountTags(request.getTagIds());
        boolean isCrsRequired = CarsUtil.isCarPlusCar(request.getVatNo()) && !request.isVirtualCar();
        AddCarBuilderDataPreSetup dataPreSetup = addCarBuilder.processingPreData(newPlateNo, memberId, request.getCarModelCode(), isCrsRequired);
        Cars car = addCarBuilder.processCarsAddSingleRequest(request, dataPreSetup.getCarBaseInfoToAdd(), dataPreSetup.isCrsCarNoExist(),
            dataPreSetup.getLatestExistingCarsInfo(), subscribeStationCode, dataPreSetup.getCheckedCarModelCode());
        // orderNos 包含格上訂單和經銷商訂單編號
        List<String> orderNos = checkCrsCarNoExistThenUpdate(dataPreSetup.getCars(), dataPreSetup.isCrsCarNoExist(),
            dataPreSetup.getOrderNos(), newPlateNo, dataPreSetup.getMemberInfo());

        if (!orderNos.isEmpty() && dataPreSetup.isCrsCarNoExist()) {
            orderNos.forEach(orderNo -> buChangeService.addBuChangeLogByCar(car, orderNo));
        }

        validateSubscribeAndDiscountLevelEligibility(car);

        carsRepository.save(car);
        if (CarDefine.Launched.tbc == car.getLaunched()) {
            notifyService.notifyAddCarFromCrs(newPlateNo);
        }
    }

    public void checkCarExistence(String plateNo) {
        Cars car = findByPlateNo(plateNo);
        if (car != null) {
            if (car.getIsDeleted()) {
                throw new SubscribeException(CAR_ALREADY_IN_SUBSCRIBE_SYSTEM_AND_IS_DELETED);
            } else {
                throw new SubscribeException(CAR_ALREADY_IN_SUBSCRIBE_SYSTEM);
            }
        }
    }

    @Transactional(readOnly = true)
    public List<? extends CarResponse> getCarResponse(CarCriteria criteria, Integer limit, Integer offset) {
        List<CarResponse> result = new ArrayList<>();
        Map<String, CarRegistration> carRegistrationMap = carRegistrationService.getAllCarRegistrations().stream().collect(Collectors.toMap(CarRegistration::getVatNo, Function.identity()));
        Class<? extends CarResponse> clazz = criteria instanceof CommonCarCriteria ? CarCommonResponse.class : CarResponse.class;
        List<CarBrandModelDTO> carBrandModelDTOList = carsRepository.searchQuery(criteria, limit, offset);
        List<String> carModelCodes = carBrandModelDTOList.stream()
            .map(CarBrandModelDTO::getModel)
            .filter(Objects::nonNull)
            .map(CarModel::getCarModelCode)
            .collect(Collectors.toList());
        Map<String, List<CarModelImage>> carModelImageMap = carModelService.findByCarModelImages(carModelCodes)
            .stream().collect(Collectors.groupingBy(CarModelImage::getCarModelCode));
        Map<Integer, SubscribeLevel> subscribeLevelMap = subscribeLevelService.getAll()
            .stream().collect(Collectors.toMap(SubscribeLevel::getLevel, Function.identity()));

        List<String> plateNos = carBrandModelDTOList.stream().map(CarBrandModelDTO::getCar)
            .map(Cars::getPlateNo).collect(Collectors.toList());
        Map<String, List<ProcessingOrder>> processingOrdersMap = CollectionUtils.isNotEmpty(criteria.getPlateNo())
            ? getProcessingOrdersMap(plateNos) : Collections.emptyMap();

        for (CarBrandModelDTO dto : carBrandModelDTOList) {
            CarResponse carResponse;
            Cars car = dto.getCar();
            CarRegistration carRegistration = StringUtils.isNotBlank(car.getVatNo()) ? carRegistrationMap.get(car.getVatNo()) : null;
            try {
                carResponse = clazz.getConstructor(Cars.class, CarModel.class, List.class, CarBrand.class, CarRegistration.class).newInstance(
                    car,
                    dto.getModel(),
                    Optional.ofNullable(dto.getModel()).map(carModel -> carModelImageMap.get(carModel.getCarModelCode())).orElse(null),
                    dto.getBrand(),
                    carRegistration);
            } catch (NoSuchMethodException | InvocationTargetException | InstantiationException | IllegalAccessException e) {
                carResponse = new CarResponse(
                    car,
                    dto.getModel(),
                    dto.getModel() == null ? null : carModelImageMap.get(dto.getModel().getCarModelCode()),
                    dto.getBrand(),
                    carRegistration);
            }
            Integer level = car.getSubscribeLevel();
            if (level != null) {
                SubscribeLevel subscribeLevel = subscribeLevelMap.get(level);
                carResponse.setLevel(new SubscribeLevelResponse(subscribeLevel));
                if (subscribeLevelService.isDiscountLevelEnabled(car, subscribeLevel)) {
                    carResponse.setDiscountLevel(new SubscribeLevelResponse(subscribeLevelMap.get(subscribeLevel.getDiscountLevel())));
                }
            }

            carResponse.setProcessOrders(processingOrdersMap.getOrDefault(carResponse.getPlateNo(), Collections.emptyList()));
            Optional.ofNullable(carResponse.getImages()).orElse(new ArrayList<>())
                .forEach(img -> img.setPaths(img.getPaths().stream()
                    .map(path -> {
                        if (!path.startsWith("http")) {
                            path = gcsUrl + path;
                        }
                        return path;
                    }).collect(Collectors.toList())));

            result.add(carResponse);
        }
        return result;
    }

    public List<CarResponse> getCarInfoList(List<String> plateNos, List<String> brandCodes, List<String> carModelCodes) {
        if (CollectionUtils.isEmpty(plateNos)) {
            return Collections.emptyList();
        }

        CarCriteria criteria = new CarCriteria();
        criteria.setPlateNo(plateNos);
        criteria.setBrandCode(brandCodes);
        criteria.setCarModelCode(carModelCodes);
        List<? extends CarResponse> results = getCarResponse(criteria, null, null);

        // 為所有車輛添加額外屬性
        results.forEach(this::enhanceWithCarProperties);

        return new ArrayList<>(results);
    }

    public CarResponse getCarInfo(String plateNo) {
        CarCriteria criteria = new CarCriteria();
        criteria.setPlateNo(Collections.singletonList(plateNo));
        List<? extends CarResponse> result = getCarResponse(criteria, null, null);

        if (result.isEmpty()) {
            throw new SubscribeException(CAR_NOT_FOUND);
        }

        CarResponse carResponse = result.get(0);
        enhanceWithCarProperties(carResponse);
        return carResponse;
    }

    // 提取的共用方法
    private void enhanceWithCarProperties(CarResponse carResponse) {
        try {
            CarPropertyResp carProperty = lrentalServer.getCarProperty(carResponse.getPlateNo());
            Optional.ofNullable(carProperty).ifPresent(cp -> {
                carResponse.setStatusName(cp.getStatusName());
                carResponse.setPurposeCodeName(cp.getPurposeCodeName());
            });
        } catch (Exception e) {
            // ignore
        }
    }

    public CarResponseWithChangeLogs getCarInfoWithChangeLogs(String plateNo) {
        CarResponse carResponse = getCarInfo(plateNo);
        List<EntityChangeLog> changeLogs = entityChangeLogService.getByMainEntityAndPrimaryKey(Cars.class, plateNo);
        return new CarResponseWithChangeLogs(carResponse, changeLogs);
    }


    /**
     * 由車籍號碼拿取車輛資訊
     */
    public CarResponse getCarInfoByCarNo(String carNo) {
        CarCriteria criteria = new CarCriteria();
        criteria.setCarNo(carNo);
        List<? extends CarResponse> result = getCarResponse(criteria, null, null);
        if (result.isEmpty()) {
            throw new SubscribeException(CAR_NOT_FOUND);
        } else {
            return result.get(0);
        }
    }

    public CarResponseWithChangeLogs getCarInfoByCarNoWithChangeLogs(String carNo) {
        CarResponse carResponse = getCarInfoByCarNo(carNo);
        List<EntityChangeLog> changeLogs = entityChangeLogService.getByMainEntityAndPrimaryKey(Cars.class, carResponse.getPlateNo());
        return new CarResponseWithChangeLogs(carResponse, changeLogs);
    }

    public CarCommonResponse getCommonCarInfoByCarNo(String carNo, Integer acctId) {
        CarCriteria criteria = new CommonCarCriteria();
        criteria.setCarNo(carNo);
        List<? extends CarResponse> result = getCarResponse(criteria, null, null);
        if (result.isEmpty()) {
            throw new SubscribeException(CAR_NOT_FOUND);
        }

        CarCommonResponse response = (CarCommonResponse) result.get(0);
        if (!Objects.equals(CarDefine.CarStatus.Free.getCode(), response.getCarStatus()) || !Objects.equals(CarDefine.Launched.open, response.getLaunched())) {
            throw new SubscribeException(CAR_NOT_FOUND);
        }
        SubscribeLevel subscribeLevel = subscribeLevelService.findByLevel(response.getSubscribeLevel());
        response.setLevel(new SubscribeLevelResponse(subscribeLevel));
        if (subscribeLevelService.isDiscountLevelEnabled(response, subscribeLevel)) {
            response.setDiscountLevel(new SubscribeLevelResponse(subscribeLevelService.getDiscountLevelBySubscriptionLevel(subscribeLevel)));
        }
        if (StringUtils.isNotBlank(response.getLocationStationCode())) {
            Stations stations = stationService.findByStationCode(response.getLocationStationCode());
            response.setLocationGeoRegion(stations.getLocateGeoRegion());
        }
        if (acctId != null) {
            setWishlistFlag(Collections.singletonList(response), acctId);
        }
        return response;
    }

    /**
     * 取得SeaLand車籍資訊
     */
    public List<SeaLandCarResponse> getSeaLandCars() {
        CarCriteria criteria = new CarCriteria();
        criteria.setIsSealandLaunched(true);
        List<CarBrandModelDTO> carBrandModelDTOList = carsRepository.searchQuery(criteria, null, null);
        Map<Integer, SubscribeLevel> levelMap = subscribeLevelService.getAll().stream().collect(Collectors.toMap(SubscribeLevel::getLevel, Function.identity()));
        return carBrandModelDTOList.stream().map(dto -> new SeaLandCarResponse(dto, levelMap.get(dto.getCar().getSubscribeLevel()))).collect(Collectors.toList());
    }

    /**
     * 透過車牌號碼取得CarDTO
     */
    public List<CarBrandModelDTO> getCarBrandModelDTOByCars(List<String> plateNos) {
        CarCriteria criteria = new CarCriteria();
        criteria.setPlateNo(plateNos);
        return carsRepository.searchQuery(criteria, null, null);
    }


    public Cars findByPlateNo(String plateNo) {
        return carsRepository.findByPlateNo(plateNo);
    }

    public Cars findByCarNo(String carNo) {
        return Optional.ofNullable(carsRepository.findByCarNo(carNo)).orElseThrow(() -> new SubscribeException(CAR_NOT_FOUND));
    }

    public List<Cars> findByCrsCarNo(Integer crsCarNo) {
        return carsRepository.findByCrsCarNo(crsCarNo);
    }

    public Cars findByCarModelCode(String carModelCode) {
        return carsRepository.findByCarModelCode(carModelCode);
    }

    /**
     * 取得車子地區廠牌資料
     * [區域, [車輛類別,車輛廠牌]]
     */
    public List<InventoryResponse> getCarsRegionInventory(CarsCondition carsCondition, Integer skip, Integer limit) {
        // <stationCode, Stations>
        Map<String, Stations> stationMap = stationService.findAll().stream().collect(Collectors.toMap(Stations::getStationCode, Function.identity()));
        // response
        // <區域, 區域所有庫存>
        Map<String, InventoryResponse> geoRegionMap = Maps.newHashMap();
        // <區域, <車輛類別,車輛廠牌>>
        Map<String, Map<CarDefine.CarState, Set<com.carplus.subscribe.model.inventory.CarBrand>>> carStateMap = Maps.newHashMap();
        CommonCarCriteria criteria = new CommonCarCriteria();
        boolean hasCarsConditionAnyFieldBeenSet = carsCondition.hasAnyFieldSet();
        if (hasCarsConditionAnyFieldBeenSet) {
            BeanUtils.copyProperties(carsCondition, criteria, carplus.common.utils.BeanUtils.ignorePropertyNames(carsCondition));
        }
        setSearchPrerequisites(criteria);
        List<? extends CarResponse> carList = getCarResponse(criteria, Integer.MAX_VALUE, 0);
        if (hasCarsConditionAnyFieldBeenSet) {
            if (skip == null || limit == null || skip < 0 || limit <= 0) {
                throw new BadRequestException("略過筆數和呈現筆數不可為空，且略過筆數必須大於等於0，呈現筆數必須大於0");
            }
            criteria.setSkip(skip);
            criteria.setLimit(limit);
            carList = filterAndSort(carList, criteria).getList();
        }
        // 訂閱車
        for (CarResponse carResponse : carList) {
            Stations station = stationMap.get(carResponse.getLocationStationCode());
            List<GeoDefine.GeoRegion> geoRegions = Lists.newArrayList();
            if (station == null) {
                geoRegions.add(GeoDefine.GeoRegion.E);
                geoRegions.add(GeoDefine.GeoRegion.C);
                geoRegions.add(GeoDefine.GeoRegion.S);
                geoRegions.add(GeoDefine.GeoRegion.N1);
            } else {
                geoRegions.add(GeoDefine.GeoRegion.valueOf(station.getLocateGeoRegion()));
            }
            geoRegions.add(null);

            for (GeoDefine.GeoRegion geo : geoRegions) {
                // 加庫存數量
                String geoRegionCode = geo == null ? null : GeoDefine.GeoRegion.enumToCode(geo);
                InventoryResponse carInventory = geoRegionMap.get(geoRegionCode);
                if (carInventory == null) {
                    carInventory = InventoryResponse.builder()
                        .geoRegionEnum(geo)
                        .geoRegion(geoRegionCode)
                        .geoRegionName(geo == null ? "不限" : GeoDefine.GeoRegion.enumToName(geo))
                        .geoInfos(Lists.newArrayList())
                        .build();
                }
                carInventory.setGeoInventory(carInventory.getGeoInventory() + 1);
                geoRegionMap.put(geoRegionCode, carInventory);

                // 拿到區域下 訂閱類別列表
                Map<CarDefine.CarState, Set<com.carplus.subscribe.model.inventory.CarBrand>> stateInventory = Optional.ofNullable(carStateMap.get(geoRegionCode)).orElseGet(Maps::newHashMap);
                // 拿到訂閱類別列表下 車輛品牌列表
                Set<com.carplus.subscribe.model.inventory.CarBrand> brandCodes = Optional.ofNullable(stateInventory.get(carResponse.getCarState())).orElseGet(Sets::newHashSet);
                // add 車輛品牌
                brandCodes.add(com.carplus.subscribe.model.inventory.CarBrand.builder().brandCode(carResponse.getCarBrand().getBrandCode()).brandNameEn(carResponse.getCarBrand().getBrandNameEn().toUpperCase()).build());
                // 儲存
                stateInventory.put(carResponse.getCarState(), brandCodes);
                carStateMap.put(geoRegionCode, stateInventory);
            }
        }
        return geoRegionMap.entrySet()
            .stream()
            .filter(entry -> !Objects.equals(entry.getKey(), GeoDefine.GeoRegion.E.name()))
            .map(entry -> {
                String geo = entry.getKey();
                InventoryResponse result = entry.getValue();
                // Map to list
                Map<CarDefine.CarState, Set<com.carplus.subscribe.model.inventory.CarBrand>> states = carStateMap.get(geo);
                List<GeoInfo> geoInfos = states.entrySet()
                    .stream()
                    .map(state -> GeoInfo.builder()
                        .carState(state.getKey())
                        .carBrands(state.getValue())
                        .build())
                    .collect(Collectors.toList());
                result.setGeoInfos(geoInfos);
                return result;
            })
            .sorted((o1, o2) -> {
                if (o1.getGeoRegionEnum() == null) {
                    return -1;
                }
                if (o2.getGeoRegionEnum() == null) {
                    return 1;
                }
                return o1.getGeoRegionEnum().compareTo(o2.getGeoRegionEnum());
            })
            .collect(Collectors.toList());
    }

    public Page<? extends CarResponse> searchByPage(PageRequest pageRequest, CarCriteria criteria, Integer acctId) {

        int limit = pageRequest.getLimit();
        int offset = pageRequest.getSkip();

        if (criteria.getCampaignId() != null) {
            CarsCondition carsCondition = criteria instanceof CommonCarCriteria
                ? campaignService.<CampaignCommonDetailResponse>get(criteria.getCampaignId(), false).getCarsCondition()
                : campaignService.<CampaignInternalDetailResponse>get(criteria.getCampaignId(), true).getCarsCondition();

            BeanUtils.copyProperties(carsCondition, criteria);
        }

        setSearchPrerequisites(criteria);

        long total = carsRepository.count(criteria);
        if (0 == total) {
            return Page.of(0, Collections.emptyList(), offset, limit);
        }

        List<? extends CarResponse> responseList = getCarResponse(criteria, limit, offset);
        if (criteria instanceof CommonCarCriteria) {
            // 如果有 acctId，需要設置收藏標記
            if (acctId != null) {
                setWishlistFlag(responseList, acctId);
            }
            return filterAndSort(responseList, criteria);
        } else {
            List<String> plateNos = responseList.stream().map(CarResponse::getPlateNo).collect(Collectors.toList());

            Map<String, List<ProcessingOrder>> processingOrdersMap = getProcessingOrdersMap(plateNos);

            responseList.forEach(car -> car.setProcessOrders(processingOrdersMap.getOrDefault(car.getPlateNo(), Collections.emptyList())));
        }
        return Page.of(total, responseList, offset, limit);
    }

    private void setWishlistFlag(List<? extends CarResponse> carResponses, Integer acctId) {

        // 獲取所有車牌號碼
        List<String> plateNos = carResponses.stream()
            .map(CarResponse::getPlateNo)
            .collect(Collectors.toList());

        // 查詢該用戶的收藏清單
        CommonCarWishlistCriteria criteria = new CommonCarWishlistCriteria();
        criteria.setLimit(CarPlusConstant.CAR_WISHLIST_LIMIT_PER_USER);
        criteria.setAcctId(acctId);
        criteria.setPlateNos(plateNos);

        Page<? extends CarWishlistResponse> wishlistPage = carWishlistService.searchByPage(criteria);

        // 建立收藏車牌集合，用於檢查
        Set<String> wishlistPlateNos = wishlistPage.getList().stream()
            .map(CarWishlistResponse::getPlateNo)
            .collect(Collectors.toSet());

        // 設置每輛車的收藏標記
        carResponses.forEach(car -> {
            if (car instanceof CarCommonResponse) {
                ((CarCommonResponse) car).setInWishlist(wishlistPlateNos.contains(car.getPlateNo()));
            }
        });
    }

    protected <T extends CarCriteria> void setSearchPrerequisites(T criteria) {
        criteria.setIsDelete(false);
        if (criteria instanceof CommonCarCriteria) {
            criteria.setLaunched(Collections.singletonList(CarDefine.Launched.open));
            criteria.setCarStatuses(Collections.singletonList(CarDefine.CarStatus.Free.getCode()));
        }
    }

    private Map<String, List<ProcessingOrder>> getProcessingOrdersMap(List<String> plateNos) {
        if (CollectionUtils.isEmpty(plateNos)) {
            return Collections.emptyMap();
        }

        CompletableFuture<Map<String, List<OrderQueryResponse>>> orderFuture = CompletableFuture.supplyAsync(() ->
            orderService.getProcessOrdersByPlateNos(plateNos).stream()
                .collect(Collectors.groupingBy(OrderQueryResponse::getPlateNo)));

        CompletableFuture<Map<String, List<DealerOrderQueryResponse>>> dealerOrderFuture = CompletableFuture.supplyAsync(() ->
            dealerOrderService.getProcessingDealerOrdersByPlateNos(plateNos).stream()
                .collect(Collectors.groupingBy(DealerOrderQueryResponse::getPlateNo)));

        return CompletableFuture.allOf(orderFuture, dealerOrderFuture)
            .thenApply(v -> {
                Map<String, List<ProcessingOrder>> combinedOrdersMap = new HashMap<>();

                orderFuture.join().forEach((plateNo, orderList) ->
                    combinedOrdersMap.put(plateNo, orderList.stream()
                        .map(order -> new ProcessingOrder(
                            order.getOrderNo(),
                            Optional.ofNullable(order.getStartDate()).orElse(order.getExpectStartDate()),
                            Optional.ofNullable(order.getEndDate()).orElse(order.getExpectEndDate())
                        )).collect(Collectors.toList())));

                dealerOrderFuture.join().forEach((plateNo, dealerOrderList) ->
                    combinedOrdersMap.merge(plateNo, dealerOrderList.stream()
                            .map(dealerOrder ->
                                new ProcessingOrder(
                                    dealerOrder.getOrderNo(),
                                    Optional.ofNullable(dealerOrder.getSubscriptionInfo().getDepartDate()).orElse(dealerOrder.getSubscriptionInfo().getExpectDepartDate()),
                                    Optional.ofNullable(dealerOrder.getSubscriptionInfo().getReturnDate()).orElse(dealerOrder.getSubscriptionInfo().getExpectReturnDate())
                                )).collect(Collectors.toList()),
                        (existing, newList) -> {
                            existing.addAll(newList);
                            return existing;
                        }));

                return combinedOrdersMap;
            }).join();
    }

    private Page<? extends CarResponse> filterAndSort(List<? extends CarResponse> carResponses, CarCriteria criteria) {
        CommonCarCriteria commonCarCriteria = (CommonCarCriteria) criteria;
        Map<String, Stations> stationsMap = stationService.findAll().stream().collect(Collectors.toMap(Stations::getStationCode, s -> s));

        // 建立基本排序比較器
        Comparator<CarCommonResponse> comparator;
        switch (commonCarCriteria.getOrderBy()) {
            case MILEAGE:
                comparator = Comparator.comparing(CarCommonResponse::getCurrentMileage, Comparator.nullsFirst(Comparator.naturalOrder()));
                break;
            case MFG_YEAR:
                comparator = Comparator.comparing(CarCommonResponse::getMfgYear, Comparator.nullsFirst(Comparator.naturalOrder()));
                break;
            case DISCOUNT_MONTH_FEE:
                comparator = Comparator.comparing(CarCommonResponse::getDiscountMonthlyFee, Comparator.nullsFirst(Comparator.naturalOrder()));
                break;
            case MONTH_FEE:
                comparator = Comparator.comparing(CarCommonResponse::getMonthlyFee, Comparator.nullsFirst(Comparator.naturalOrder()));
                break;
            case USE_MONTH_FEE:
            default:
                comparator = Comparator.comparing(CarCommonResponse::getUseMonthlyFee, Comparator.nullsFirst(Comparator.naturalOrder()));
        }

        // 處理排序方向
        if (SubscribeCarDefine.Sort.DESC == commonCarCriteria.getSort()) {
            comparator = comparator.reversed();
        }

        // 建立複合排序比較器：優先比較車體介紹的有無，其次才是基本排序比較
        Comparator<CarCommonResponse> compositeComparator = Comparator
            // 首先比較車體介紹的有無 (有介紹的排前面)
            .comparing((CarCommonResponse car) -> StringUtils.isNotBlank(car.getCnDesc()), Comparator.reverseOrder())
            // 其次依照基本排序
            .thenComparing(comparator);

        List<? extends CarResponse> response = carResponses.stream()
            .map(car -> ((CarCommonResponse) car)).peek(car -> {
                if (StringUtils.isNotBlank(car.getLocationStationCode())) {
                    car.setLocationGeoRegion(stationsMap.get(car.getLocationStationCode()).getLocateGeoRegion());
                }
            })
            .filter(car -> isCarMatchingCommonCriteria(car, commonCarCriteria))
            .sorted(compositeComparator).collect(Collectors.toList());

        // 分頁處理
        int endPoint = commonCarCriteria.getSkip() + commonCarCriteria.getLimit();
        if (endPoint > response.size()) {
            endPoint = response.size();
        }

        return Page.of(
            response.size(),
            (response.isEmpty() || response.size() < commonCarCriteria.getSkip())
                ? new ArrayList<>()
                : response.subList(commonCarCriteria.getSkip(), endPoint),
            commonCarCriteria.getSkip(),
            commonCarCriteria.getLimit()
        );
    }

    protected boolean isCarMatchingCommonCriteria(CarCommonResponse car, CommonCarCriteria commonCarCriteria) {
        // 篩選月費範圍
        if (Objects.nonNull(commonCarCriteria.getMonthFeeStart()) && car.getUseMonthlyFee() < commonCarCriteria.getMonthFeeStart()) {
            return false;
        }
        if (Objects.nonNull(commonCarCriteria.getMonthFeeEnd()) && car.getUseMonthlyFee() > commonCarCriteria.getMonthFeeEnd()) {
            return false;
        }

        // 篩選出廠年份範圍
        int carMfgYear = parseMfgYear(car.getMfgYear());
        if (carMfgYear != -1) {
            if (Objects.nonNull(commonCarCriteria.getMfgYearFrom()) && carMfgYear < commonCarCriteria.getMfgYearFrom()) {
                return false;
            }
            return !Objects.nonNull(commonCarCriteria.getMfgYearTo()) || carMfgYear <= commonCarCriteria.getMfgYearTo();
        }

        return true;
    }

    private int parseMfgYear(String mfgYear) {
        if (StringUtils.isNotBlank(mfgYear)) {
            try {
                return Integer.parseInt(mfgYear);
            } catch (NumberFormatException e) {
                // 如果無法解析為數字，則返回-1表示忽略此篩選條件
                return -1;
            }
        }
        return -1;
    }

    public CsvUtil.ByteArrayOutputStream2ByteBuffer carsCsvGenerate(CarCriteria criteria) {
        List<? extends CarResponse> carsList = searchByPage(new PageRequest(Integer.MAX_VALUE, 0), criteria, null).getList();
        Map<String, CarRegistration> carRegistrationMap = carRegistrationService.getAllCarRegistrations().stream().collect(Collectors.toMap(CarRegistration::getVatNo, Function.identity()));
        List<CarRegistrationCSV> csvList = carsList.stream().map(c -> {
            CarRegistration carRegistration = null;
            CarRegistrationCSV csv = new CarRegistrationCSV();
            csv.setCarState(Optional.ofNullable(c.getCarState()).map(CarDefine.CarState::getName).orElse(null));
            csv.setPlateNo(c.getPlateNo());
            csv.setCarNo(c.getCarNo());
            csv.setBrandNameEn(Optional.ofNullable(c.getCarBrand()).map(CarBrand::getBrandNameEn).orElse(null));
            csv.setBrandCode(Optional.ofNullable(c.getCarBrand()).map(CarBrand::getBrandCode).orElse(null));
            csv.setCarModelCode(Optional.ofNullable(c.getCarModel()).map(CarModel::getCarModelCode).orElse(null));
            csv.setCarModelName(Optional.ofNullable(c.getCarModel()).map(CarModel::getCarModelName).orElse(null));
            csv.setDisplacement(c.getDisplacement());
            csv.setSeat(c.getSeat());
            csv.setFuelType(c.getFuelType());
            csv.setFuelTypeName(Optional.ofNullable(c.getFuelType()).map(CarDefine.FuelType::getName).orElse(null));
            csv.setMfgYear(c.getMfgYear());
            csv.setCurrentMileage(c.getCurrentMileage());
            csv.setEquipIds(c.getEquipIds());
            csv.setEquips(Optional.ofNullable(c.getEquipIds()).orElse(new ArrayList<>()).stream().map(Object::toString).collect(Collectors.joining(";")));
            csv.setLocationStationCode(c.getLocationStationCode());
            csv.setSubscribeLevel(c.getSubscribeLevel());
            csv.setTagIds(c.getTagIds());
            csv.setLaunched(c.getLaunched());
            csv.setLaunchedName(c.getLaunched().getName());
            csv.setCarStatus(Optional.ofNullable(CarDefine.CarStatus.of(c.getCarStatus())).map(CarDefine.CarStatus::getName).orElse(null));
            csv.setProcessOrders(Optional.ofNullable(c.getProcessOrders()).orElse(new ArrayList<>()).stream().map(ProcessingOrder::getOrderNo).collect(Collectors.joining(";")));
            csv.setTags(Optional.ofNullable(c.getTagIds()).orElse(new ArrayList<>()).stream().map(Object::toString).collect(Collectors.joining(";")));
            csv.setCrsCarNo(c.getCrsNo());
            csv.setIsSealandLaunched(c.getIsSealandLaunched());
            csv.setSealandLaunchedName(c.getIsSealandLaunched() ? CarDefine.Launched.open.name() : CarDefine.Launched.close.name());
            csv.setEnergyTypeName(Optional.ofNullable(c.getEnergyType()).map(CarDefine.EnergyType::getName).orElse(null));
            csv.setVatNo(c.getVatNo());
            if (StringUtils.isNotBlank(c.getVatNo())) {
                carRegistration = carRegistrationMap.get(c.getVatNo());
                Optional.ofNullable(carRegistration).ifPresent(cr -> {
                    csv.setOwnerName(cr.getShortName());
                });
            }
            return csv;
        }).collect(Collectors.toList());
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CsvUtil.createCsv(
            csvList,
            CarRegistrationCSV.getCsvOutputColumn(),
            true,
            ',',
            out,
            Charset.forName("big5"),
            CarRegistrationCSV.class
        );
        return out;
    }

    /**
     * 修改舊短租及訂閱車籍所在站所
     **/
    @Transactional(transactionManager = "mysqlTransactionManager")
    public int updateLocationStation(@NonNull String plateNo, @Nullable String newLocationStationCode) {
        Stations stations = StringUtils.isBlank(newLocationStationCode) ? null : stationService.findByStationCode(newLocationStationCode);
        Cars car = Optional.ofNullable(carsRepository.findByPlateNo(plateNo)).orElseThrow(() -> new ServerException("無法取得訂閱車輛：" + plateNo));
        int updateCount = carsRepository.updateLocationStation(plateNo, stations);
        if (updateCount == 0) {
            if (Objects.equals(CarDefine.CarStatus.Subscribed.getCode(), car.getCarStatus())) {
                throw new BadRequestException("車輛已被訂閱，欲更新之站所無法調度");
            } else if (!Objects.equals(CarDefine.CarStatus.Free.getCode(), car.getCarStatus())) {
                throw new BadRequestException("車輛已出車，不可更新所在站所");
            }
            return updateCount;
        }
        return updateCount;
    }

    /**
     * 修改舊短租及訂閱車籍所在站所
     **/
    @Transactional(transactionManager = "mysqlTransactionManager")
    public int updateCurrentMileage(String plateNo, int currentMileage) {
        int resultCars = carsRepository.updateCurrentMileage(plateNo, currentMileage);
        return (resultCars == 1) ? 1 : 0;
    }

    /**
     * 更新車輛狀態
     *
     * @param car       車輛
     * @param carStatus 狀態
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateStatus(Cars car, CarDefine.CarStatus carStatus) {
        if (car.isVirtualCar()) {
            return;
        }

        if (CarDefine.CarStatus.Free.equals(carStatus)) {
            car.setBookingOrderNo(null);
        }

        car.setCarStatus(carStatus.getCode());
        carsRepository.save(car);
    }

    /**
     * 放車
     * 當取消訂單 (合約狀態 已訂車→取消)
     * 或完成還車 (合約狀態 已出車未還車 → 已還車 or 已還車未結案)
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void releaseCar(Orders orders, Cars car) {
        if (canReleaseCar(orders, car)) {
            updateStatus(car, CarDefine.CarStatus.Free);
            if (CarsUtil.isCarPlusCar(car.getVatNo())) {
                CarBaseInfoSearchResponse carBase = crsService.getCar(car.getPlateNo());
                if (carBase != null && (Objects.equals(BuIdEnum.secondHand.getCode(), carBase.getBuId()) || carBase.getBuId().equals(BuIdEnum.carCenter.getCode()))) {
                    crsService.unsubscribeCarControl(carBase);
                }
            }
        }
    }

    /**
     * 檢查是否可以放車
     */
    private boolean canReleaseCar(Orders orders, Cars car) {
        return isValidOrderStatus(orders) && isValidCarStatus(car, orders);
    }

    private boolean isValidOrderStatus(Orders orders) {
        HashSet<Integer> validStatuses = new HashSet<>();

        validStatuses.add(OrderStatus.CANCEL.getStatus());
        validStatuses.add(OrderStatus.CLOSE.getStatus());
        validStatuses.add(OrderStatus.ARRIVE_NO_CLOSE.getStatus());
        validStatuses.add(OrderStatus.STOLEN.getStatus());
        if (!orders.getIsNewOrder()) {
            validStatuses.add(OrderStatus.CREDIT_REJECT.getStatus());
        }

        return validStatuses.contains(orders.getStatus());
    }

    private boolean isValidCarStatus(Cars car, Orders order) {
        HashSet<String> isReleasableCarStatus = new HashSet<>();
        isReleasableCarStatus.add(CarDefine.CarStatus.Subscribed.getCode());
        isReleasableCarStatus.add(CarDefine.CarStatus.BizOut.getCode());
        isReleasableCarStatus.add(CarDefine.CarStatus.Stolen.getCode());

        boolean hasNoActiveOrders = CollectionUtils.isEmpty(orderService.getProcessOrdersByPlateNoExcludingOrderNo(car.getPlateNo(), order.getOrderNo()))
            && CollectionUtils.isEmpty(dealerOrderService.getProcessingDealerOrdersByPlateNo(car.getPlateNo()));

        return isReleasableCarStatus.contains(car.getCarStatus()) && hasNoActiveOrders;
    }

    /**
     * 如果CRS_NO為空，則更新CRS_NO
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateCrsCarNo(Cars cars) {
        if (!cars.isVirtualCar() && cars.getCrsCarNo() == null && CarsUtil.isCarPlusCar(cars.getVatNo())) {
            CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(cars.getPlateNo());
            cars.setCrsCarNo(Optional.ofNullable(carBaseInfoSearchResponse).map(CarBaseInfoSearchResponse::getCarNo).orElse(null));
            carsRepository.save(cars);
        }
    }

    /**
     * 保證金付款鎖車
     */
    public void lockCar(String plateNo, CarDefine.CarStatus carStatus, String mainContractNo) {
        carsRepository.lockCar(plateNo, carStatus, mainContractNo);
    }

    /**
     * 車輛下架
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void launchClose(@NonNull String plateNo) {
        Cars car = carsRepository.findById(plateNo).orElseThrow(() -> new BadRequestException("無法取得訂閱車輛：" + plateNo));
        car.setLaunched(CarDefine.Launched.close);
        carsRepository.save(car);
    }

    /**
     * 查詢長租資料匯入舊短租車籍和訂閱車籍
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void importCars(@NonNull CarRegistrationImport carImport) {

        //裝備資訊
        Map<Integer, CarEquip> equipMap = carEquipService.findAllCarEquips(true).stream()
            .collect(Collectors.toMap(CarEquip::getEquipId, equip -> equip));
        //車輛標籤
        Map<Integer, CarTag> tagsMap = carTagService.findAllCarTags(true).stream()
            .collect(Collectors.toMap(CarTag::getTagId, tag -> tag));
        //車型資訊
        List<String> carModelCodes = carModelService.findAll().stream()
            .map(CarModel::getCarModelCode).collect(Collectors.toList());

        carImport.validate(carModelCodes, equipMap, tagsMap);

        CarModel carModel = Optional.ofNullable(carModelService.findByCarModelCode(carImport.getCarModelCode()))
            .orElseThrow(() -> new BadRequestException("無對照短租車型編號"));

        // 檢查Crs是否有此車
        CarBaseInfoSearchResponse crsCar = crsService.getCar(carImport.getPlateNo());
        if (crsCar == null) {
            throw new SubscribeException(CRS_CAR_NOT_FOUND);
        }
        checkIsSubscribeCar(crsCar);
        // ================ mysql ================
        Cars car = findByPlateNo(carImport.getPlateNo());
        if (car != null) {
            throw new BadRequestException("車籍資料已存在訂閱車");
        }
        car = new Cars();
        car.setPlateNo(carImport.getPlateNo());
        car.setLaunched(CarDefine.Launched.close);
        car.setCarNo(encodePlateNo(carImport.getPlateNo()));
        car.setCarStatus(CarDefine.CarStatus.Free.getCode());
        car.setCarModelCode(carImport.getCarModelCode());
        car.setDisplacement(BigDecimal.valueOf(crsCar.getCarSpecInfoResponse().getCylinder()));
        car.setFuelType(carImport.getFuelType());
        car.setMfgYear(Optional.ofNullable(crsCar.getCarBase().getPublishDate()).map(mfgDate -> mfgDate.substring(0, 4)).orElseThrow(() -> new BadRequestException("找不到出廠年份")));
        car.setRegisStationCode(subscribeStationCode);
        car.setOwnerStationCode(subscribeStationCode);
        car.setLocationStationCode(carImport.getLocationStationCode());
        car.setCurrentMileage(carImport.getCurrentMileage());
        car.setEquipIds(carImport.getEquipIds());
        car.setSeat(carImport.getSeat());
        car.setSubscribeLevel(subscribeLevelService.findByLevel(carImport.getSubscribeLevel()).getLevel());
        car.setTagIds(carImport.getTagIds());
        car.setCarState(CarDefine.CarState.valueOf(carImport.getCarStat()));
        car.setCarType(CarDefine.CarType.of(carModel.getCarKind().getName()));
        car.setProjectCar(Optional.ofNullable(crsService.searchProjectCar(crsCar.getCarNo())).map(PurchaseProjectCarSearchResponse::getIsProjectCar).orElse(false));
        Optional.ofNullable(carImport.getVatNo()).ifPresent(car::setVatNo);
        Optional.ofNullable(carImport.getPrepWorkdays()).ifPresent(car::setPrepWorkdays);
        carsRepository.save(car);
    }

    /**
     * 訂閱車籍資料驗證 && 查詢長租資料匯入舊短租車籍和訂閱車籍(批次)
     */
    @Get(group = CarRegistrationValidate.class, key = "#uuid", ttl = 60 * 60 * 2)
    public CarRegistrationValidate validateCarsDataAndSaveCSVCarsAndCarRegistration(List<CarRegistrationCSV> list,
                                                                                    String uuid,
                                                                                    String memberId) {
        //裝備資訊
        Map<Integer, CarEquip> equipMap = carEquipService.findAllCarEquips(true).stream()
            .collect(Collectors.toMap(CarEquip::getEquipId, equip -> equip));
        //車輛標籤
        Map<Integer, CarTag> tagsMap = carTagService.findAllCarTags(true).stream()
            .collect(Collectors.toMap(CarTag::getTagId, tag -> tag));
        //車型資訊
        Map<String, CarModel> subscribeCarModelMap = carModelService.findAll().stream()
            .collect(Collectors.toMap(CarModel::getCarModelCode, carModel -> carModel));
        //站點資訊
        List<String> stationList = stationService.findAll().stream().map(Stations::getStationCode).collect(Collectors.toList());
        //訂閱方案
        List<Integer> levels = new ArrayList<>();
        subscribeLevelService.getAll().forEach(sl -> levels.add(sl.getLevel()));
        Map<String, CarRegistration> carRegistrationMap = carRegistrationService.getAllCarRegistrations().stream()
            .collect(Collectors.toMap(CarRegistration::getVatNo, carRegistration -> carRegistration));
        Set<String> plateNos = list.stream().map(CarRegistrationCSV::getPlateNo).map(String::toUpperCase).collect(Collectors.toSet());
        Map<String, CarBaseInfoSearchResponse> carBaseInfoMap = new HashMap<>();
        for (List<String> subList : Lists.partition(new ArrayList<>(plateNos), 100)) {
            carBaseInfoMap.putAll(crsService.getCars(new ArrayList<>(subList)));
        }
        Set<Integer> crsCarNos = list.stream().map(CarRegistrationCSV::getCrsCarNo).filter(Objects::nonNull).filter(crsCarNo -> crsCarNo > 1).collect(Collectors.toSet());
        Map<Integer, CarBaseInfoSearchResponse> carBaseInfoMapByCrsCarNo = new HashMap<>();
        for (List<Integer> subList : Lists.partition(new ArrayList<>(crsCarNos), 100)) {
            carBaseInfoMapByCrsCarNo.putAll(crsService.getCarsByCrsCarNo(new ArrayList<>(subList)));
        }
        List<String> processingCars = orderService.getBookingAndDepartOrdersByPlateNos(plateNos).stream().map(OrderQueryResponse::getPlateNo).collect(Collectors.toList());
        List<String> existPlateNos = new ArrayList<>();
        List<CarRegistrationCSV> addCars = new ArrayList<>();
        List<CarRegistrationCSV> errorCars = new ArrayList<>();
        List<CarRegistrationValidateError> errorList = new ArrayList<>();

        for (CarRegistrationCSV csv : list) {
            List<String> errorMessages = csv.validate(subscribeCarModelMap, equipMap, tagsMap, stationList, carRegistrationMap);
            CarBaseInfoSearchResponse carBaseInfo = carBaseInfoMap.get(csv.getPlateNo());
            if (carBaseInfo == null && csv.getCrsCarNo() != null && csv.getCrsCarNo() > 0) {
                carBaseInfo = carBaseInfoMapByCrsCarNo.get(csv.getCrsCarNo());
            }

            CarImportLogicValidate carImportLogicValidate = new CarImportLogicValidate(errorMessages, csv, levels, carBaseInfo, existPlateNos, !processingCars.contains(csv.getPlateNo()));

            carImportLogicValidate.validate();
            //將資料存入失敗的List
            if (!errorMessages.isEmpty()) {
                errorList.add(CarRegistrationValidateError.builder().plateNo(csv.getPlateNo()).errorMessage(errorMessages).build());
                csv.setErrorMessage(String.join(";", errorMessages));
                errorCars.add(csv);
            } else {
                addCars.add(csv);
                existPlateNos.add(csv.getPlateNo());
            }
        }

        CarRegistrationValidate carValidate = CarRegistrationValidate.builder()
            .rows(addCars)
            .errorRows(errorCars)
            .errorList(errorList).build();

        MemberInfo memberInfo = authorityServer.getMemberInfos(memberId).get(0);
        // 寫入 cars 車籍
        if (!carValidate.getRows().isEmpty()) {
            this.importCSVCars(carValidate.getRows(), memberInfo);
        }
        return carValidate;
    }

    /**
     * CSV匯入車籍
     * <br>
     * 因檢查主要在{@link CarsService#validateCarsDataAndSaveCSVCarsAndCarRegistration(List, String, String)} (List csvs, String uuid)}檢查<br>
     * 故要使用時需用上述方法檢驗或需自行檢驗 <br/>
     * Steps <br/>
     * 1. {@link AddCarBuilder#processingPreData(String, String, String, boolean)}  <br/>
     * 2. {@link AddCarBuilder#processCarRegistrationCSV(CarRegistrationCSV, CarBaseInfoToAddResponse, boolean, Map, String, String)} <br/>
     * 3. {@link #checkCrsCarNoExistThenUpdate(List, boolean, List, String, MemberInfo)} <br/>
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void importCSVCars(@NonNull List<CarRegistrationCSV> carsCsvs, MemberInfo memberInfo) {
        List<Cars> cars = new ArrayList<>();

        for (CarRegistrationCSV carsCsv : carsCsvs) {
            checkForMutuallyExclusiveDiscountTags(carsCsv.getTagIds());

            AddCarBuilderDataPreSetup dataPreSetup = addCarBuilder.processingPreData(carsCsv.getPlateNo(), null, carsCsv.getCarModelCode(), true);
            Cars car = addCarBuilder.processCarRegistrationCSV(carsCsv, dataPreSetup.getCarBaseInfoToAdd(),
                dataPreSetup.isCrsCarNoExist(), dataPreSetup.getLatestExistingCarsInfo(), subscribeStationCode,
                dataPreSetup.getCheckedCarModelCode());
            List<String> orderNos = checkCrsCarNoExistThenUpdate(dataPreSetup.getCars(), dataPreSetup.isCrsCarNoExist(),
                dataPreSetup.getOrderNos(), carsCsv.getPlateNo(), memberInfo);

            if (!orderNos.isEmpty() && dataPreSetup.isCrsCarNoExist()) {
                orderNos.forEach(orderNo -> buChangeService.addBuChangeLogByCar(car, orderNo));
            }

            cars.add(car);
        }
        carsRepository.saveAll(cars);
    }

    /**
     * 產生車籍Excel對照範例檔
     */
    public ExcelUtil carMappingExampleFile() {
        String id = "代碼";
        String name = "名稱";
        String originalCode = "短租原始代碼";
        String brandName = "廠牌名稱";
        List<Map<String, Object>> equips = carEquipService.findAllCarEquips(true).stream().map(equip -> {
            Map<String, Object> map = new HashMap<>();
            map.put(id, equip.getEquipId().toString());
            map.put(name, equip.getCnName());
            return map;
        }).collect(Collectors.toList());
        List<Map<String, Object>> carTags = carTagService.findAllCarTags(true).stream().map(tag -> {
            Map<String, Object> map = new HashMap<>();
            map.put(id, tag.getTagId().toString());
            map.put(name, tag.getCnName());
            return map;
        }).collect(Collectors.toList());
        List<Map<String, Object>> stations = stationService.findAll().stream().filter(Stations::getIsSubscribe).map(station -> {
            Map<String, Object> map = new HashMap<>();
            map.put(id, station.getStationCode());
            map.put(name, station.getStationName());
            return map;
        }).collect(Collectors.toList());
        List<Map<String, Object>> fuelTypes = Arrays.stream(CarDefine.FuelType.values()).map(fuelType -> {
            Map<String, Object> map = new HashMap<>();
            map.put(id, fuelType.name());
            map.put(name, fuelType.getName());
            return map;
        }).collect(Collectors.toList());

        List<Map<String, Object>> energyTypes = Arrays.stream(CarDefine.EnergyType.values()).map(fuelType -> {
            Map<String, Object> map = new HashMap<>();
            map.put(id, fuelType.name());
            map.put(name, fuelType.getName());
            return map;
        }).collect(Collectors.toList());

        List<Map<String, Object>> subCarModels = carModelService.findBySearch(new CarModelQueryRequest()).stream().map(carModelResponse -> {
            Map<String, Object> map = new HashMap<>();
            map.put(id, carModelResponse.getCarModelCode());
            map.put(name, carModelResponse.getCarModelName());
            map.put(brandName, carModelResponse.getBrandName());
            map.put(originalCode, carModelResponse.getOriginalCarModelCode());
            return map;
        }).collect(Collectors.toList());
        List<Map<String, Object>> carState = Arrays.stream(CarDefine.CarState.values()).map(state -> {
            Map<String, Object> map = new HashMap<>();
            map.put(id, state.name());
            map.put(name, state.getName());
            return map;
        }).collect(Collectors.toList());
        List<Map<String, Object>> launchedType = Arrays.stream(CarDefine.Launched.values()).map(state -> {
            Map<String, Object> map = new HashMap<>();
            map.put(id, state.name());
            map.put(name, state.getName());
            return map;
        }).collect(Collectors.toList());
        List<Map<String, Object>> carRegistration = carRegistrationService.getAllCarRegistrations().stream().map(cr -> {
            Map<String, Object> map = new HashMap<>();
            map.put(id, cr.getVatNo());
            map.put(name, cr.getShortName());
            return map;
        }).collect(Collectors.toList());
        ExcelUtil.SheetData equipsSheet = new ExcelUtil.SheetData("equip", Arrays.asList(id, name), equips);
        ExcelUtil.SheetData carTagsSheet = new ExcelUtil.SheetData("tags", Arrays.asList(id, name), carTags);
        ExcelUtil.SheetData stationSheet = new ExcelUtil.SheetData("station", Arrays.asList(id, name), stations);
        ExcelUtil.SheetData fuelTypeSheet = new ExcelUtil.SheetData("fuelType", Arrays.asList(id, name), fuelTypes);
        ExcelUtil.SheetData stateSheet = new ExcelUtil.SheetData("state", Arrays.asList(id, name), carState);
        ExcelUtil.SheetData launched = new ExcelUtil.SheetData("launched", Arrays.asList(id, name), launchedType);
        ExcelUtil.SheetData carModelSheet = new ExcelUtil.SheetData("subCarModels", Arrays.asList(id, name, brandName, originalCode), subCarModels);
        ExcelUtil.SheetData energyTypeSheet = new ExcelUtil.SheetData("energyType", Arrays.asList(id, name), energyTypes);
        ExcelUtil.SheetData carRegistrationSheet = new ExcelUtil.SheetData("vatNo", Arrays.asList(id, name), carRegistration);
        return new ExcelUtil().builder().setfileName("cars_批量匯入編號對照表.xlsx")
            .addSheetData(equipsSheet)
            .addSheetData(carTagsSheet)
            .addSheetData(stationSheet)
            .addSheetData(fuelTypeSheet)
            .addSheetData(stateSheet)
            .addSheetData(carModelSheet)
            .addSheetData(launched)
            .addSheetData(energyTypeSheet)
            .addSheetData(carRegistrationSheet)
            .build();
    }

    /**
     * 產生車籍匯入範例檔
     */
    public byte[] carImportExampleFile() {
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CarRegistrationCSV csv = new CarRegistrationCSV();
        csv.setCarState(CarDefine.CarState.OLD.name());
        csv.setPlateNo("XXX-XXXX");
        csv.setCarModelCode("S0003");
        csv.setCurrentMileage(1234567);
        csv.setEquips("23;16");
        csv.setLocationStationCode("201");
        csv.setSubscribeLevel(1);
        csv.setLaunchedName(CarDefine.Launched.close.name());
        csv.setStdPrice(999999);
        csv.setIsSealandLaunched(false);
        csv.setGearType(CarDefine.GearType.at);
        csv.setCarType(CarDefine.CarType.sedan);
        csv.setColorDesc("顏色描述");
        csv.setCnDesc("中文描述");
        csv.setVatNo("12208883");

        CsvUtil.createCsv(
            new ArrayList<>(Collections.singletonList(csv)),
            CarRegistrationCSV.getCarCsvInputHead().toArray(new String[0]),
            true,
            ',',
            out,
            Charset.forName("big5"),
            CarRegistrationCSV.class
        );
        return out.toByteArray();
    }

    /**
     * 產生範例ZIP檔(包含Excel對照表與CSV匯入範例)
     */
    public byte[] exportExampleZip() {
        try {
            ExcelUtil excelUtil = carMappingExampleFile();
            byte[] csvFile = carImportExampleFile();
            Map<String, byte[]> map = Maps.newHashMap();
            map.put(excelUtil.getFileName(), excelUtil.toByte());
            map.put("cars_批量匯入範本.csv", csvFile);
            return ZipUtil.zipAndEncrypt(map, null);
        } catch (IOException e) {
            throw new ServerException(e);
        }
    }

    /**
     * 長租是否為購入
     */
    public boolean checkCarIsSelfCar(String plateNo) {
        List<Da41Entity> lRentalCars = lrentalServer.getLrentalCars(Collections.singletonList(plateNo));
        if (lRentalCars.isEmpty()) {
            throw new BadRequestException(String.format("%s不在長租資料中", plateNo));
        }
        String carStatus = lRentalCars.get(0).getDaCarStatus();
        if (StringUtils.isNotBlank(carStatus)) {
            // 0.購入 3.繳銷重領 5.出售
            return carStatus.equalsIgnoreCase("0");
        }
        return false;
    }

    public CarBrandModelDTO getCarBrandModelByPlateNo(String plateNo) {
        return carsRepository.getCarBrandModelByPlateNo(plateNo);
    }


    private void checkSubscribeLevel(List<Integer> levels, Integer subscribeLevel) {
        if (!levels.contains(subscribeLevel)) {
            throw new BadRequestException("訂閱車方案 level:" + subscribeLevel + " 不存在");
        }
    }


    /**
     * 車輛上架
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void launchOpen(@NonNull String plateNo) {
        Cars car = carsRepository.findById(plateNo).orElseThrow(() -> new BadRequestException("無法取得訂閱車輛：" + plateNo));
        car.setLaunched(CarDefine.Launched.open);
        carsRepository.save(car);
    }


    /**
     * 車損下架
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void launchAccident(@NonNull String plateNo) {
        Cars car = carsRepository.findById(plateNo).orElseThrow(() -> new BadRequestException("無法取得訂閱車輛：" + plateNo));
        car.setLaunched(CarDefine.Launched.accident);
        carsRepository.save(car);
    }

    /**
     * 車輛不使用
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void launchDeprecate(@NonNull String plateNo) {
        Cars car = carsRepository.findById(plateNo).orElseThrow(() -> new BadRequestException("無法取得訂閱車輛：" + plateNo));
        car.setLaunched(CarDefine.Launched.deprecate);
        carsRepository.save(car);
    }

    /**
     * 車輛不使用並通知營業
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void launchChangeAndNotify(@NonNull Integer crsCarNo, boolean launched, String changeType, String changeMemo) {
        List<Cars> carsList = findByCrsCarNo(crsCarNo);
        List<String> plateNos = carsList.stream().map(Cars::getPlateNo).collect(Collectors.toList());
        for (Cars cars : carsList) {
            if (!launched) {
                launchDeprecate(cars.getPlateNo());
            } else {
                launchClose(cars.getPlateNo());
            }
            updateSealandLaunch(cars.getPlateNo(), true);
        }
        if (!launched && CollectionUtils.isNotEmpty(plateNos)) {
            Map<String, List<String>> notifyPlateNos = orderService.getBookingAndDepartOrdersByPlateNos(plateNos)
                .stream()
                .collect(Collectors.groupingBy(
                    OrderQueryResponse::getPlateNo,
                    Collectors.mapping(OrderQueryResponse::getOrderNo, Collectors.toList())
                ));
            dealerOrderService.getProcessingDealerOrdersByPlateNos(plateNos)
                .stream()
                .collect(Collectors.groupingBy(
                    DealerOrderQueryResponse::getPlateNo,
                    Collectors.mapping(DealerOrderQueryResponse::getOrderNo, Collectors.toList())
                ))
                .forEach((plateNo, orderNos) ->
                    notifyPlateNos.merge(plateNo, orderNos, (existing, additional) -> {
                        existing.addAll(additional);
                        return existing;
                    })
                );

            notifyPlateNos.entrySet().removeIf(entry ->
                // 移除無效車牌或無訂單編號
                StringUtils.isBlank(entry.getKey()) || CollectionUtils.isEmpty(entry.getValue())
            );

            if (!notifyPlateNos.isEmpty()) {
                notifyService.notifyCarChange(notifyPlateNos, changeType, changeMemo);
            }
        }
    }

    /**
     * SL是否上架
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateSealandLaunch(@NonNull String plateNo, boolean isLaunch) {
        Cars car = carsRepository.findById(plateNo).orElseThrow(() -> new BadRequestException("無法取得訂閱車輛：" + plateNo));
        car.setSealandLaunched(isLaunch);
        carsRepository.save(car);
    }

    /**
     * 檢查是否訂閱車
     */
    private void checkIsSubscribeCar(CarBaseInfoSearchResponse carBaseInfoSearchResponse) {
        if (BuIdEnum.isNotValidForSubscribe(carBaseInfoSearchResponse.getBuId())) {
            throw new SubscribeException(NOT_FOR_SUBSCRIBE);
        }
        if (!carBaseInfoSearchResponse.getCarBase().getCarStatus().equals("0")) {
            throw new SubscribeException(LONG_RENTAL_CAR_NOT_FOUND);
        }
    }

    /**
     * 自動撥車
     */
    @Transactional
    public Integer changeBu(AutoChangeBuRequest autoChangeBuRequest, String companyId, String memberId) {
        Cars car = findByPlateNo(autoChangeBuRequest.getPlateNo());
        if (car == null) {
            throw new SubscribeException(CAR_NOT_FOUND);
        }
        if (!CarsUtil.isCarPlusCar(car.getVatNo())) {
            throw new SubscribeException(CAR_NOT_ALLOW_CHANGE_BU);
        }
        CarBaseInfoSearchResponse carBase = crsService.getCar(autoChangeBuRequest.getPlateNo());
        if (carBase == null) {
            throw new SubscribeException(CRS_CAR_NOT_FOUND);
        }
        if (BuIdEnum.lRental.getCode().equals(carBase.getBuId())) {
            throw new SubscribeException(CRS_CAR_BELONG_LRENTAL);
        }
        List<OrderQueryResponse> orderQueryResponseList = orderService.getProcessOrdersByPlateNos(Collections.singletonList(autoChangeBuRequest.getPlateNo())).stream()
            .filter(order -> order.getStatus() == OrderStatus.BOOKING.getStatus()).collect(Collectors.toList());
        DealerOrderCriteria dealerOrderCriteria = new DealerOrderCriteria();
        dealerOrderCriteria.setOrderStatus(Collections.singletonList(ContractStatus.CREATE.getCode()));
        dealerOrderCriteria.setPlateNo(Collections.singletonList(car.getPlateNo()));
        List<DealerOrderQueryResponse> dealerOrderQueryResponseList = dealerOrderService.searchPage(dealerOrderCriteria, Integer.MAX_VALUE, 0);
        if (orderQueryResponseList.isEmpty() && dealerOrderQueryResponseList.isEmpty()) {
            throw new SubscribeException(GOING_CONTRACT_NOT_FOUND);
        }
        // 2024/12/12 CYB-16058 暫時拔除
//        else if (orderQueryResponseList.size() + dealerOrderQueryResponseList.size() > 1) {
//            throw new SubscribeException(SubscribeHttpExceptionCode.MULTIPLE_BOOKING_SAME_PLATE_NO_ORDERS);
//        }

        Instant expectStartDate;
        Instant expectEndDate;
        if (!orderQueryResponseList.isEmpty()) {
            OrderQueryResponse orderQueryResponse = orderQueryResponseList.get(0);
            expectStartDate = orderQueryResponse.getExpectStartDate();
            expectEndDate = orderQueryResponse.getExpectEndDate();
        } else {
            DealerOrderQueryResponse dealerOrderQueryResponse = dealerOrderQueryResponseList.get(0);
            expectStartDate = dealerOrderQueryResponse.getSubscriptionInfo().getExpectDepartDate();
            expectEndDate = dealerOrderQueryResponse.getSubscriptionInfo().getExpectReturnDate();
        }
        String orderNo = autoChangeBuRequest.getOrderNo();
        if (autoChangeBuRequest.getChangeType() == BuChangeEnum.ChangeTypeOfAssign.AUTO_BATCH_CHANGE) {
            buChangeService.changeBuFromPreowned(orderNo, carBase, memberId, expectStartDate, expectEndDate, autoChangeBuRequest.getLicenseExpDate(), car);
        } else if (autoChangeBuRequest.getChangeType() == BuChangeEnum.ChangeTypeOfAssign.CHANGE) {
            buChangeService.generalChange(orderNo, carBase, memberId, expectStartDate, expectEndDate, car, autoChangeBuRequest.getAttachmentId());
        } else if (autoChangeBuRequest.getChangeType() == BuChangeEnum.ChangeTypeOfAssign.BATCH_CHANGE) {
            buChangeService.batchChange(orderNo, carBase, memberId, expectStartDate, expectEndDate, car, autoChangeBuRequest.getAttachmentId());
        }
        car.setCrsCarNo(carBase.getCarNo());
        carsRepository.save(car);
        if (autoChangeBuRequest.getLicenseExpDate() != null && StringUtils.isNotBlank(orderNo)) {
            RequisitionCreateRequest request;
            if (!orderQueryResponseList.isEmpty()) {
                Orders orders = orderService.getOrder(orderNo);
                SubscribeInsurance subscribeInsurance = insuranceService.generateSubscribeInsurance(
                    orders,
                    null,
                    CompulsoryInsurance.addAssign,
                    autoChangeBuRequest.getLicenseExpDate(),
                    car.getBuChangeMasterId() + ""
                );
                request = insuranceService.generateInsuranceRequest(subscribeInsurance, memberId, ConditionOrderSource.Carplus);
            } else {
                DealerOrder dealerOrder = dealerOrderService.getOrder(orderNo);
                DealerOrderQueryResponse dealerOrderQueryResponse = dealerOrderService.createDealerOrderQueryResponseWithUserInfo(dealerOrder);
                SubscribeInsurance subscribeInsurance = insuranceService.generateSubscribeInsurance(
                    dealerOrderQueryResponse,
                    null, CompulsoryInsurance.addAssign, autoChangeBuRequest.getLicenseExpDate(),
                    car.getBuChangeMasterId() + "");
                request = insuranceService.generateInsuranceRequest(subscribeInsurance, memberId, ConditionOrderSource.SeaLand);
            }
            if (!insuranceService.hasValidArbitraryPolicy(car.getPlateNo())) {
                insuranceService.addBatchInsurance(companyId, memberId, Collections.singletonList(request));
            }
        }
        return car.getBuChangeMasterId();
    }


    /**
     * 自動撥還車
     */
    @Transactional
    public Integer autoChangeReturnBu(String orderNo, String plateNo, String memberId) {
        Cars car = findByPlateNo(plateNo);
        return autoChangeReturnBu(orderNo, car, memberId);
    }

    /**
     * 自動撥還車
     */
    @Transactional
    public Integer autoChangeReturnBu(String orderNo, Cars car, String memberId) {
        if (car == null) {
            throw new SubscribeException(CAR_NOT_FOUND);
        }
        if (car.getBuChangeMasterId() == null) {
            throw new SubscribeException(CAR_NOT_HAVE_ASSIGN_NO);
        }
        CarBaseInfoSearchResponse carBase = crsService.getCar(car.getPlateNo());
        if (carBase == null) {
            throw new SubscribeException(CRS_CAR_NOT_FOUND);
        }
        if (!BuIdEnum.subscribe.getCode().equals(carBase.getBuId())) {
            throw new SubscribeException(CRS_CAR_NOT_BELONG_SUBSCRIBE);
        }
        List<OrderQueryResponse> orderQueryResponseList = orderService.getProcessOrdersByPlateNos(Collections.singletonList(car.getPlateNo()));
        orderQueryResponseList = orderQueryResponseList.stream().filter(order -> order.getStatus() == OrderStatus.BOOKING.getStatus()).collect(Collectors.toList());
        if (!orderQueryResponseList.isEmpty()) {
            throw new SubscribeException(CRS_CAR_CAN_NOT_CHANGE_BECAUSE_GOING_ORDER);
        }
        Integer buChangeMasterId = crsService.autoChangeReturnBu(orderNo, carBase, memberId, car.getBuChangeMasterId());
        car.setBuChangeMasterId(null);
        carsRepository.save(car);
        return buChangeMasterId;
    }

    public Integer sellToPreowned(String plateNo, SellToPreownedRequest sellToPreownedRequest, String memberId) {
        // 收集所有驗證錯誤
        List<String> validationErrors = new ArrayList<>();

        Cars cars = findByPlateNo(plateNo);
        if (cars == null) {
            validationErrors.add(CAR_NOT_FOUND.getMsg());
        }
        CarBaseInfoSearchResponse carBase = crsService.getCar(plateNo);
        if (carBase == null) {
            validationErrors.add(CRS_CAR_NOT_FOUND.getMsg());
        }
        if (cars != null) {
            if (cars.isVirtualCar()) {
                validationErrors.add("虛擬車沒有長租契約");
            }
        }
        if (carBase != null && !Objects.equals(BuIdEnum.subscribe.getCode(), carBase.getBuId())) {
            validationErrors.add(CRS_CAR_NOT_BELONG_SUBSCRIBE.getMsg());
        }

        // 如果有任何驗證錯誤，拋出包含所有錯誤訊息的異常
        if (!validationErrors.isEmpty()) {
            throw LogicException.of(CarPlusCode.FORBIDDEN, String.join("；", validationErrors).concat("，則不發動賣車到中古"));
        }

        return crsService.findLocationAndSellToPreowned(null, carBase, sellToPreownedRequest.getAreaCode(), sellToPreownedRequest.getLocationCode(), memberId);
    }

    private Map<String, Cars> getCarsMapFromSpec(Specification<Cars> spec) {
        return carsRepository.findAll(spec).stream().collect(Collectors.toMap(Cars::getPlateNo, Function.identity()));
    }

    private Specification<Cars> getActiveCarsSpecification() {
        return (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get(Cars.Fields.isDeleted), false));
            predicateList.add(cb.notEqual(root.get(Cars.Fields.launched), CarDefine.Launched.deprecate));
            return cb.and(predicateList.toArray(new Predicate[0]));
        };
    }

    private Map<String, Cars> getActiveCarsMap() {
        return getCarsMapFromSpec(getActiveCarsSpecification());
    }

    private Map<String, Cars> getActiveCarsWithNullMfgMonthMap() {
        return getCarsMapFromSpec(getActiveCarsSpecification()
            .and((root, query, cb) -> cb.isNull(root.get(Cars.Fields.mfgMonth))));
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void syncStdPriceToCar() {
        Map<String, Cars> carsMap = getActiveCarsMap();

        for (List<String> carsList : Lists.partition(new ArrayList<>(carsMap.keySet()), 100)) {
            Map<String, CarBaseInfoSearchResponse> crsMap = crsService.getCars(carsList);
            List<Cars> saveList = new ArrayList<>();
            for (String plateNo : carsList) {
                Cars cars = carsMap.get(plateNo);
                Optional.ofNullable(crsMap.get(plateNo)).map(CarBaseInfoSearchResponse::getCarBase).map(CarBase::getStdPrice).ifPresent(stdPrice -> {
                    cars.setStdPrice(stdPrice);
                    saveList.add(cars);
                });
            }
            carsRepository.saveAll(saveList);
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void syncEnergyTypeToCar() {
        Map<String, Cars> carsMap = getActiveCarsMap();

        for (List<String> carsList : Lists.partition(new ArrayList<>(carsMap.keySet()), 100)) {
            Map<String, CarBaseInfoSearchResponse> crsMap = crsService.getCars(carsList);
            List<Cars> saveList = new ArrayList<>();
            for (String plateNo : carsList) {
                Cars cars = carsMap.get(plateNo);
                Optional.ofNullable(crsMap.get(plateNo)).map(CarBaseInfoSearchResponse::getCarSpecInfoResponse).map(CarSpecInfoResponse::getCarPlusEnergyCode).ifPresent(energyCode -> {
                    cars.setEnergyType(CarDefine.EnergyType.of(energyCode));
                    saveList.add(cars);
                });
            }
            carsRepository.saveAll(saveList);
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void syncCrsCarNoAndLaunchedToCar() {
        List<Cars> carsList = carsRepository.findAll(getActiveCarsSpecification()).stream().filter(cars -> cars.getCrsCarNo() == null).collect(Collectors.toList());

        for (List<Cars> subCarsList : Lists.partition(carsList, 100)) {
            Map<String, CarBaseInfoSearchResponse> crsMap = crsService.getCars(subCarsList.stream().map(Cars::getPlateNo).collect(Collectors.toList()));
            List<Cars> saveList = new ArrayList<>();
            for (Cars cars : subCarsList) {
                Optional.ofNullable(crsMap.get(cars.getPlateNo())).ifPresent(carBase -> {
                    cars.setCrsCarNo(carBase.getCarNo());
                    if (carBase.getCarBase().getCarStatus().equals("5")) {
                        // carStatus = 5表示已出售
                        cars.setLaunched(CarDefine.Launched.deprecate);
                    }
                    saveList.add(cars);
                });
            }
            carsRepository.saveAll(saveList);
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void syncCrsIsProjectCar() {
        List<Cars> carsList = carsRepository.findAll().stream().filter(cars -> cars.getCrsCarNo() != null).collect(Collectors.toList());


        for (List<Cars> subCarsList : Lists.partition(carsList, 100)) {
            Map<Integer, PurchaseProjectCarSearchResponse> crsMap = crsService.searchProjectCars(subCarsList.stream().map(Cars::getCrsCarNo).collect(Collectors.toList()));
            List<Cars> saveList = new ArrayList<>();
            for (Cars cars : subCarsList) {
                Optional.ofNullable(crsMap.get(cars.getCrsCarNo())).ifPresent(projectCar -> {
                    boolean isProjectCar = Optional.ofNullable(projectCar.getIsProjectCar()).orElse(false);
                    cars.setProjectCar(isProjectCar);
                    saveList.add(cars);
                });
            }
            carsRepository.saveAll(saveList);
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void syncCarsMfgMonthFromCrs() {
        Map<String, Cars> carsMap = getActiveCarsWithNullMfgMonthMap();

        for (List<String> plateNoList : Lists.partition(new ArrayList<>(carsMap.keySet()), 100)) {
            Map<String, CarBaseInfoSearchResponse> crsCarsMap = crsService.getCars(plateNoList);

            List<Cars> saveList = plateNoList.stream()
                .map(carsMap::get)
                .peek(car -> Optional.ofNullable(crsCarsMap.get(car.getPlateNo()))
                    .map(CarBaseInfoSearchResponse::getCarBase)
                    .map(CarBase::getPublishDate)
                    .ifPresent(publishDate -> {
                        publishDate = publishDate.trim();
                        if (publishDate.length() >= 7) {
                            car.setMfgMonth(publishDate.substring(5, 7));
                        }
                    })).collect(Collectors.toList());

            carsRepository.saveAll(saveList);
        }
    }

    public Map<String, Integer> countByStationCode(List<String> stationCodeList) {
        return carsRepository.countByStationCode(stationCodeList);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public List<PlateNoQueryResponse> getCarAndLocation(PlateNoQueryRequest request) {
        List<Cars> cars = carsRepository.findByPlateNoList(request.getPlateNoList());
        return cars.stream().map(car -> PlateNoQueryResponse.builder()
            .plateNo(car.getPlateNo())
            .status(Objects.requireNonNull(CarDefine.CarStatus.of(car.getCarStatus())).getName())
            .km(car.getCurrentMileage())
            .areaId(CarPlusConstant.departmentMasterCode)
            .areaName(CarPlusConstant.departmentMasterName)
            .locationId(Optional.ofNullable(car.getLocationStationCode()).orElse(CarPlusConstant.SUBSCRIBE_MANAGEMENT_DEPT_CODE))
            .locationName(Optional.ofNullable(car.getStations()).map(Stations::getStationName).orElse(CarPlusConstant.departmentName))
            .build()).collect(Collectors.toList());
    }

    public List<Cars> getIdleCar() {
        return carsRepository.getIdleCar();
    }


    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateLocationStationBasedOnSgType(MainContract mainContract) {
        Cars cars = findByPlateNo(mainContract.getPlateNo());
        updateLocationStationBasedOnSgType(mainContract, cars);
    }

    /**
     * 根據是否調度入庫來更新車輛所在站所
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateLocationStationBasedOnSgType(MainContract mainContract, Cars cars) {
        try {
            if (cars.isVirtualCar()) {
                return;
            }
            if (cars.getCrsCarNo() == null) {
                updateCrsCarNo(cars);
            }

            List<CarHistorySearchRep> carHistoryList = lrentalServer.getCarHistory(Collections.singletonList(cars.getCrsCarNo()));
            if (CollectionUtils.isEmpty(carHistoryList)) {
                return;
            }
            String stationCode;
            // 入庫則寫入查回所在
            CarHistorySearchRep carHistory = carHistoryList.get(0);
            if (carHistory.isCarInStock()) {
                stationCode = crsService.getPreownedStationByStationCode(String.valueOf(carHistory.getSgAuto())).getCode();
            } else {
                // 出庫但為新單取消則不做任何事
                if (ContractStatus.CANCEL.getCode() == mainContract.getStatus()) {
                    return;
                }
                // 出庫則依照合約狀態寫入預計出車站點/實際還車站點
                stationCode = ContractStatus.COMPLETE.getCode() == mainContract.getStatus()
                    ? mainContract.getReturnStationCode() : mainContract.getDepartStationCode();
            }
            Stations stations = stationService.findByStationCode(stationCode);
            cars.setLocationStationCode(stations.getStationCode());
        } catch (Exception e) {
            log.error("updateLocationStationBasedOnSgType 更新車輛所在失敗", e);
            cars.setLocationStationCode(subscribeStationCode);
        } finally {
            carsRepository.save(cars);
        }
    }

    /**
     * 新增車籍時，<br/>
     * 1. 如果 該 plateNo 於 CRS 存在 CrsCarNo，檢查 cars.crsCarNo 是否已有車牌擁有相同號碼 <br/>
     * 2. 則將擁有該CrsCarNo原車牌 launched 改為 deprecate、carStatus 改為 00 <br/>
     * 3-1. 更新主約車牌
     * 3-2. 以及該車牌進行中訂單(orders)，直接換車，並在訂單註解新增：{{原始車牌號碼}}繳銷重領為{{新增車牌號碼}} <br/>
     * return true : 則之後執行<br/>
     * 4-1. 新車牌比照原車牌`subscribeLevel`,`carModelCode`,`carState`,`launched=close`,`isSealandLaunched=false` <br/>
     * 4-2. 撥車單號(cars.buChangeMasterId), (主約車號更新) <br/>
     * {@link #updateAfterCheckCrsCarNo(List, String, MemberInfo, List, List, List)}  <br/>
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public List<String> checkCrsCarNoExistThenUpdate(List<Cars> cs, boolean isCrsCarNoExist, List<String> orderNos, String newPlateNo,
                                                     MemberInfo memberInfo) {
        if (isCrsCarNoExist) {
            List<String> existCarPlateNos = cs.stream().map(Cars::getPlateNo).collect(Collectors.toList());
            List<OrderQueryResponse> processingOrders = orderService.getProcessOrdersByPlateNos(existCarPlateNos);
            orderNos = processingOrders.stream().map(OrderQueryResponse::getOrderNo).collect(Collectors.toList());
            List<DealerOrderQueryResponse> processingDealerOrders = dealerOrderService.getProcessingDealerOrdersByPlateNos(existCarPlateNos);
            processingDealerOrders.stream().map(DealerOrderQueryResponse::getOrderNo).forEach(orderNos::add);
            updateAfterCheckCrsCarNo(cs, newPlateNo, memberInfo, processingOrders, processingDealerOrders, orderNos);
        }

        return orderNos;
    }

    @Transactional
    public void changeSameCar(Cars oriCar, Cars useCar, String orderNo) {
        if (oriCar.getBuChangeMasterId() != null) {
            useCar.setBuChangeMasterId(oriCar.getBuChangeMasterId());
            buChangeService.addBuChangeLogByCar(useCar, orderNo);
            carsRepository.save(useCar);
        }
    }

    private List<Cars> getCarsWithoutDeletedAndDeprecated(Integer carBaseInfoToAdd) {
        return findByCrsCarNo(carBaseInfoToAdd).stream()
            .filter(car -> !car.getIsDeleted() || !car.getLaunched().equals(CarDefine.Launched.deprecate))
            .collect(Collectors.toList());
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateAfterCheckCrsCarNo(List<Cars> cars, String newPlateNo, MemberInfo memberInfo, List<OrderQueryResponse> processingOrders,
                                         List<DealerOrderQueryResponse> processingDealerOrders, List<String> orderNos) {
        // 1.
        List<String> mainContractNos = processingOrders.stream()
            .map(OrderQueryResponse::getMainContractNo)
            .collect(Collectors.toList());
        List<MainContract> mainContracts = contractService.getMainContracts(mainContractNos);
        mainContracts.forEach(mainContract -> {
            // 更新主約車牌
            mainContract.setPlateNo(newPlateNo);
            contractService.updateMainContract(mainContract);
        });

        dealerOrderService.updatePlateNoForOrders(newPlateNo, processingDealerOrders);

        cars.forEach(car -> {
            // 車牌是否相同
            if (!car.getPlateNo().equals(newPlateNo)) {
                // 2.
                car.setLaunched(CarDefine.Launched.deprecate);
                car.setCarStatus(CarDefine.CarStatus.Free.getCode());
                car.setBookingOrderNo(null);
                update(car);
                // 3.
                Remark remark = orderService.buildRemark(car.getPlateNo() + "繳銷重領為" + newPlateNo, memberInfo);

                List<String> carPlusOrderNos = orderNos.stream()
                    .filter(orderNo -> orderService.isCarPlusOrder(orderNo))
                    .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(carPlusOrderNos)) {
                    List<Orders> orders = orderService.getOrders(carPlusOrderNos);
                    orders.forEach(order -> orderService.addRemark(order, remark));
                }
            }
        });
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void populateCarBaseInfoWithOriginalCarIfExists(CarBaseInfoToAddResponse carBaseInfoToAdd) {
        findByCrsCarNo(carBaseInfoToAdd.getCrsCarNo()).stream().max(Comparator.comparing(Cars::getUpdateDate)).ifPresent(car -> {
            carBaseInfoToAdd.setCarModelCode(car.getCarModelCode());
            Optional.ofNullable(car.getCarModel()).ifPresent(carModel -> carBaseInfoToAdd.setBrandCode(carModel.getBrandCode()));
            carBaseInfoToAdd.setCarState(car.getCarState());
            carBaseInfoToAdd.setSubscribeLevel(car.getSubscribeLevel());
            carBaseInfoToAdd.setLaunched(car.getLaunched());
            carBaseInfoToAdd.setIsSealandLaunched(car.isSealandLaunched());
            carBaseInfoToAdd.setTagIds(car.getTagIds());
            carBaseInfoToAdd.setEquipIds(car.getEquipIds());
            carBaseInfoToAdd.setGearType(car.getGearType());
            carBaseInfoToAdd.setColorDesc(car.getColorDesc());
            carBaseInfoToAdd.setCnDesc(car.getCnDesc());
            carBaseInfoToAdd.setFuelType(car.getFuelType());
            carBaseInfoToAdd.setCarType(car.getCarType());
            carBaseInfoToAdd.setStdPrice(car.getStdPrice());
        });
    }

    public List<Cars> findByPlateNoList(List<String> plateNoList) {
        return carsRepository.findByPlateNoList(plateNoList);
    }
}
