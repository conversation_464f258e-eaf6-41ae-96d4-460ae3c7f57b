package com.carplus.subscribe.service;

import carplus.common.enums.HeaderDefine;
import carplus.common.redis.cache.Del;
import carplus.common.redis.cache.DelAll;
import carplus.common.response.Result;
import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.dao.InvoicesRepository;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.enums.Donate;
import com.carplus.subscribe.enums.InvoiceDefine;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.enums.PayFor;
import com.carplus.subscribe.enums.inv.AllowanceDataEnum;
import com.carplus.subscribe.enums.inv.BusinessInfoEnum;
import com.carplus.subscribe.enums.inv.InvoiceDataEnum;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.feign.FinServiceBusClient;
import com.carplus.subscribe.model.IncomeDTO;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.invoice.*;
import com.carplus.subscribe.model.invoice.v2.*;
import com.carplus.subscribe.model.payment.req.AccountRecord;
import com.carplus.subscribe.model.priceinfo.CalculateStage;
import com.carplus.subscribe.model.priceinfo.PriceInfoInterface;
import com.carplus.subscribe.model.priceinfo.PriceInfoWrapper;
import com.carplus.subscribe.model.region.Area;
import com.carplus.subscribe.model.region.City;
import com.carplus.subscribe.model.request.invoice.InvoiceCriteriaRequest;
import com.carplus.subscribe.model.response.invoice.InvoicesWithAcctId;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.FinanceServer;
import com.carplus.subscribe.server.GoSmartServer;
import com.carplus.subscribe.utils.CsvUtil;
import com.carplus.subscribe.utils.DateUtil;
import com.carplus.subscribe.utils.PriceUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.nio.charset.Charset;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.constant.CarPlusConstant.CARPLUS_COMPANY_VAT_NO;
import static com.carplus.subscribe.enums.InvoiceDefine.InvType.Biz;
import static com.carplus.subscribe.enums.InvoiceDefine.InvType.Person;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.*;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Pay;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Refund;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;

@Slf4j
@Service
public class InvoiceServiceV2 {

    @Autowired
    private InvoicesRepository invoicesRepository;
    @Autowired
    private PriceInfoService priceInfoService;
    @Autowired
    private OrderService orderService;
    @Autowired
    @Lazy
    private InvoiceServiceV2 self;
    @Autowired
    private AuthServer authServer;
    @Autowired
    private FinServiceBusClient finServiceBusClient;
    @Autowired
    private FinanceServer financeServer;
    @Autowired
    private GoSmartServer goSmartServer;

    @Value("${invoice.allow-editable.check-sysDate}")
    private boolean checkSysDate;
    @Value("${station.subscribe}")
    private String subscribeStationCode;

    /**
     * 拿取訂單發票
     */
    public List<Invoices> getInvoice(@NonNull String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        return invoicesRepository.findInvoicesByOrderNo(orders.getOrderNo());
    }

    /**
     * 官網用戶拿取發票
     */
    public List<Invoices> getInvoice(Integer acctId, @NonNull String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        if (acctId.equals(orders.getContract().getMainContract().getAcctId())) {
            return invoicesRepository.findInvoicesByOrderNo(orders.getOrderNo());
        }
        return null;
    }

    /**
     * 拿取訂單發票
     */
    @Transactional(transactionManager = "mysqlTransactionManager", propagation = Propagation.REQUIRES_NEW)
    public List<Invoices> getInvoiceWithNewTransaction(@NonNull String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        return invoicesRepository.findInvoicesByOrderNos(Collections.singletonList(orders.getOrderNo()));
    }

    /**
     * 取得每一 acctId 最新開立的發票
     */
    public List<InvoicesWithAcctId> getLatestInvoiceByAcctIds(List<Integer> acctIds) {
        List<Orders> orders = orderService.getOrdersByAcctIds(acctIds);

        Map<String, Integer> orderNoToAcctId = orders.stream()
            .collect(Collectors.toMap(
                Orders::getOrderNo,
                order -> order.getContract().getMainContract().getAcctId()
            ));
        List<String> orderNos = new ArrayList<>(orderNoToAcctId.keySet());
        List<Invoices> sortedInvoicesList = invoicesRepository.findInvoicesByOrderNosWithoutCA(orderNos);

        Map<Integer, Invoices> latestInvoicesByAcctId = new LinkedHashMap<>();
        for (Invoices invoices : sortedInvoicesList) {
            Integer acctId = orderNoToAcctId.get(invoices.getOrderNo());
            latestInvoicesByAcctId.putIfAbsent(acctId, invoices);
        }

        return latestInvoicesByAcctId.entrySet().stream()
            .map(InvoicesWithAcctId::fromInvoiceEntry)
            .collect(Collectors.toList());
    }

    /**
     * 發票開立
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public List<Invoices> createInvoice(@NonNull String orderNo, @NonNull String payFor, @NonNull List<InvoiceRequest> invoices, String memberId) {
        Orders orders = orderService.getOrder(orderNo);
        AuthUser user = authServer.getUserWithRetry(orders.getContract().getMainContract().getAcctId());
        List<Invoices> invoicesList = new ArrayList<>();
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(orderNo);
        InvoiceInfo invoiceInfo = new InvoiceInfo(invoicesRepository.findInvoicesByOrderNo(orders.getOrderNo()));
        Integer total = invoiceInfo.getTotalAmount();  // 總發票金額
        total += invoiceCreateValidate(invoices, orders, priceInfoWrapper.getList());
        int paidAmt = priceInfoWrapper.toFilter().currentReceivable().paid().wrap().getActualPrice();

        if (orders.getStatus() != OrderStatus.CANCEL.getStatus() && orders.getStatus() != OrderStatus.CLOSE.getStatus()) {
            PriceInfoWrapper refundWrapper = priceInfoWrapper.toFilter().notCategory(ETag).type(Refund).wrap();
            int refundAmt = Math.abs(refundWrapper.getActualPrice() - refundWrapper.getActualReceivePrice());
            paidAmt -= refundAmt;
            log.info("愈退款金額:{}", refundAmt);
        }
        // 檢查發票金額 == 收支金額
        // 訂閱且取消 檢查手續費金額
        if (orders.getStatus() == OrderStatus.CANCEL.getStatus()) {
            int receiveAmt = priceInfoWrapper.toFilter().currentReceivable().wrap().getActualPrice();
            receiveAmt +=
                priceInfoService.getPriceInfosByOrder(orderNo).stream()
                    .filter(orderPriceInfo -> orderPriceInfo.getCategory() == SecurityDeposit
                        || (orderPriceInfo.getRefPriceInfo() != null && orderPriceInfo.getRefPriceInfo().getCategory() == SecurityDeposit))
                    .mapToInt(OrderPriceInfo::getActualPrice).sum();
            if (!Objects.equals(total, receiveAmt)) {
                log.error("發票開立總金額:" + total + ", 手續費金額:" + receiveAmt + " 不一致");
                throw new BadRequestException("發票開立總金額:" + total + ", 手續費金額:" + receiveAmt + " 不一致");
            }
            // 檢查費用明細已收
        } else if (!Objects.equals(paidAmt, total)) {

            log.error(("發票開立總金額:" + total + ", 已收總金額:" + paidAmt + " 不一致"));
            throw new BadRequestException("發票開立總金額:" + total + ", 已收總金額:" + paidAmt + " 不一致");
        }

        for (InvoiceRequest request : invoices) {
            Invoices invoice = self.create(orders, user, subscribeStationCode, payFor, request, memberId);
            invoicesList.add(invoice);
        }
        return invoicesList;
    }

    public Integer invoiceCreateValidate(List<InvoiceRequest> invoices, Orders orders, List<OrderPriceInfo> orderPriceInfoList) {
        AtomicReference<Integer> total = new AtomicReference<>(0);
        invoices.forEach(request -> {
            total.updateAndGet(v -> v + request.getAmount());
            // 檢核發票
            invoiceValidate(orders, request, orderPriceInfoList);
        });
        return total.get();
    }

    public void invoiceValidateBeforeRecord(List<InvoiceRequest> requestList, List<OrderPriceInfo> orderPriceInfoList) {
        if (!CollectionUtils.isEmpty(requestList)) {
            requestList.forEach(request -> {
                if (!CollectionUtils.isEmpty(request.getRefPriceInfoIds())) {
                    invoicePriceInfoValidate(request.getRefPriceInfoIds(), request.getAmount(), orderPriceInfoList);
                }
            });
        }
    }

    public void invoiceValidateAmountBeforeRecord(List<AccountRecord> accountRecords, List<InvoiceRequest> requestList, String orderNo) {
        int invoiceAmt = invoicesRepository.findInvoicesByOrderNo(orderNo).stream().filter(i -> Objects.equals(i.getStatus(), InvoiceDefine.InvStatus.CREATE.name())).mapToInt(Invoices::getAmount).sum();
        invoiceAmt += requestList.stream().mapToInt(InvoiceRequest::getAmount).sum();
        int accountAmt = 0;
        for (AccountRecord accountRecord : accountRecords) {
            if (!accountRecord.isDeleted()) {
                accountAmt += accountRecord.getOriginalAmount();
                accountAmt -= accountRecord.getRefundAmount();
                accountAmt -= accountRecord.getTotalRefundAmount();
            }
        }
        if (invoiceAmt != accountAmt) {
            log.error("發票金額與收支金額不一致:發票金額{},收支金額{}", invoiceAmt, accountAmt);
            throw new SubscribeException(ORDER_AMOUNT_NOT_EQUAL_INVOICE_AMOUNT);
        }
    }

    public void invoiceValidate(Orders order, InvoiceRequest request, List<OrderPriceInfo> orderPriceInfoList) {
        request.getInvoice().validate(order.getInvoice());
        if (request.getInvoice().needFinanceValidate(Optional.ofNullable(order.getInvoice()).orElseGet(Invoice::defaultInvoice))) {
            financeServer.checkInvoice(request.getInvoice());
        }
        if (!CollectionUtils.isEmpty(request.getRefPriceInfoIds())) {
            request.setMerchandiseRelated(invoicePriceInfoValidate(request.getRefPriceInfoIds(), request.getAmount(), orderPriceInfoList));
        }
    }

    public boolean invoicePriceInfoValidate(List<Integer> refPriceInfoIds, int amount, List<OrderPriceInfo> orderPriceInfoList) {
        List<Integer> refMasterId = new ArrayList<>();
        AtomicInteger amt = new AtomicInteger();
        // 是否保證金轉收入
        AtomicBoolean isSecurityDepositMarginToRent = new AtomicBoolean(false);

        orderPriceInfoList.forEach(opi -> {
            if (refPriceInfoIds.contains(opi.getId())) {
                if (opi.getCategory() == SecurityDeposit && opi.getType() == Pay.getCode()) {
                    isSecurityDepositMarginToRent.set(true);
                }
                amt.addAndGet(opi.getActualPrice());
                if (opi.getRefPriceInfoNo() != null) {
                    refMasterId.add(opi.getRefPriceInfoNo());
                }
            }
        });

        if (isSecurityDepositMarginToRent.get()) {
            return false;
        }

        if (!refMasterId.isEmpty() && !new HashSet<>(refPriceInfoIds).containsAll(refMasterId)) {
            log.error("有關連的款項須開在同一張發票：all PriceInfoIds{},refIds{}", refPriceInfoIds, refMasterId);
            throw new SubscribeException(ORDER_PRICE_INFO_REF_NEED_COMBINED_TOGETHER);
        }

        if (amt.get() <= 0 || amount != amt.get()) {
            log.error("發票與收支不平：amt{},orderPriceInfoList{}", amt, orderPriceInfoList);
            throw new SubscribeException(ORDER_PRICE_INFO_AMOUNT_NOT_MATCH_INVOICE_AMOUNT);
        }

        // 使用共用方法來判斷商品相關狀態
        return PriceUtils.areAllPriceInfosMerchandiseRelated(orderPriceInfoList, refPriceInfoIds);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Invoices create(@NonNull Orders orders, @NonNull AuthUser user, @NonNull String stationCode, @NonNull String payFor, @NonNull InvoiceRequest request, String memberId) {
        InvoiceIssueRequest invoiceIssueRequest = buildInvoiceIssueRequest(orders, user, request);
        log.info("開立發票Request:{}", invoiceIssueRequest);

        Result<InvoiceInfoQueue> result = finServiceBusClient.createInvoice(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, memberId, invoiceIssueRequest);
        if (result.getStatusCode() == 0 && result.getData() != null) {
            Invoices invoices = buildAndSaveInvoice(orders.getOrderNo(), stationCode, payFor, request, memberId, result.getData());

            // 將發票資料重新寫入redis
            invoicesRepository.findInvoicesByOrderNo(orders.getOrderNo());
            return invoices;
        } else {
            throw new BadRequestException(Optional.of(result).map(Result::getMessage).orElse("發票開立失敗:財務中台未知錯誤"));
        }
    }

    private InvoiceIssueRequest buildInvoiceIssueRequest(Orders orders, AuthUser user, InvoiceRequest request) {
        InvoiceIssueRequest invoiceIssueRequest = new InvoiceIssueRequest();
        invoiceIssueRequest.setIssueTransactionID(UUID.randomUUID().toString().substring(0, 29));
        invoiceIssueRequest.setBusinessType(BusinessInfoEnum.BusinessType.E5);
        invoiceIssueRequest.setBusinessSubType("SUB000");
        invoiceIssueRequest.setInvoiceType(InvoiceDataEnum.InvoiceType.EC);
        invoiceIssueRequest.setVatNo(CARPLUS_COMPANY_VAT_NO);
        invoiceIssueRequest.setSellerVatNo(CARPLUS_COMPANY_VAT_NO);
        invoiceIssueRequest.setTaxType(InvoiceDataEnum.TaxType.T);
        invoiceIssueRequest.setTotalVATAmount(request.getAmount());
        invoiceIssueRequest.setNotifyBuyerName(user.getAcctName());
        invoiceIssueRequest.setNotifyBuyerEmail(user.getEmail());
        invoiceIssueRequest.setNotifyBuyerMobile(user.getMainCell());
        invoiceIssueRequest.setIsDonate(false);
        invoiceIssueRequest.setMemo(request.getMemo());

        // 公司設定統編  有統編或是捐贈不用給抬頭(BuyerName)
        if (request.getInvoice().getType().equals(Biz.getType())) {
            invoiceIssueRequest.setBuyerVatNo(request.getInvoice().getId());
            invoiceIssueRequest.setBuyerName(request.getInvoice().getTitle());
        }

        if (request.getInvoice().getCategory() == InvoiceDefine.InvCategory.DONATE.getCategory()) {
            invoiceIssueRequest.setDonateCode(Donate.HSAPF.getId());
            invoiceIssueRequest.setIsDonate(true);
        } else {
            String carrierId = Optional.ofNullable(request.getInvoice()).map(Invoice::getCarrierID).orElse("");
            // 若為會員載具，則carrierId為ECI00733M+手機
            if (request.getInvoice().getCategory() == InvoiceDefine.InvCategory.MEMBER.getCategory()) {
                carrierId = "ECI00733M" + user.getAcctId();
            }
            invoiceIssueRequest.setCarrierID1(carrierId);
            invoiceIssueRequest.setCarrierID2(carrierId);
        }
        if (InvoiceDefine.InvCategory.isCarrier(request.getInvoice().getCategory())) {
            invoiceIssueRequest.setCarrierType(InvoiceDefine.InvCategory.of(request.getInvoice().getCategory()).getCarrierType());
        }
        invoiceIssueRequest.setMemberID(user.getAcctId() + "");

        InvoiceIssueDetailRequest invoiceDetail = new InvoiceIssueDetailRequest();
        invoiceDetail.setItemNo(1);
        invoiceDetail.setQty(1);
        invoiceDetail.setItemName(request.isMerchandiseRelated() ? "汽車用品" : "租金");
        invoiceDetail.setTaxType(InvoiceDataEnum.TaxType.T);
        invoiceDetail.setItemVatPrice(request.getAmount());
        invoiceDetail.setItemVatAmount(request.getAmount());
        invoiceDetail.setDepenceDocType(InvoiceDataEnum.DepenceDocType.orders);
        invoiceDetail.setDepenceDocNo(orders.getOrderNo());
        invoiceDetail.setPlateNo(orders.getPlateNo());
        invoiceDetail.setMemo(request.getMemo());

        invoiceIssueRequest.setInvoiceIssueDetailList(Collections.singletonList(invoiceDetail));
        setCityArea(invoiceIssueRequest, user);

        return invoiceIssueRequest;
    }

    private Invoices buildAndSaveInvoice(String orderNo, String stationCode, String payFor, InvoiceRequest request, String memberId, InvoiceInfoQueue queue) {
        Invoices invoices = request.toEntity(stationCode, payFor, queue.getInvoiceNo(), orderNo);
        invoices.setIsFinBus(true);
        invoices.setIsCheckout(InvoiceDefine.InvCheckoutStatus.CHECKOUT.getStatus());
        invoices.setCreatedAt(new Date(queue.getInvoiceDate().toEpochMilli()));
        invoices.setMemberId(memberId);
        invoicesRepository.save(invoices);
        return invoices;
    }

    /**
     * 自動針對已開立發票金額與以收款金額差開立發票
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void autoCreateInvoice(Orders order, List<OrderPriceInfo> orderPriceInfoList) {
        // 建立發票
        // 自動登打與開發票
        AtomicInteger paidAmt = new AtomicInteger();
        List<Integer> invoicePriceInfoIds = orderPriceInfoList.stream()
            .filter(opi -> !opi.getCategory().equals(ETag))
            .filter(opi -> !opi.getCategory().equals(SecurityDeposit))
            .peek(opi ->
                paidAmt.addAndGet(opi.getActualReceivePrice())

            ).map(OrderPriceInfo::getId).collect(Collectors.toList());
        if (paidAmt.get() == 0) {
            return;
        }
        InvoiceRequest request = new InvoiceRequest();
        autoGenerateInvoice(order, order.getInvoice(), order.getContract().getMainContract().getAcctId());
        request.setInvoice(order.getInvoice());
        request.setAmount(paidAmt.get());
        request.setRefPriceInfoIds(invoicePriceInfoIds);
        request.setMemo(PayFor.Depart.name());

        createInvoice(order.getOrderNo(), PayFor.Depart.name(), Collections.singletonList(request), "system");
    }

    /**
     * 自動產生發票抬頭
     */
    private void autoGenerateInvoice(Orders orders, Invoice invoice, Integer acctId) {
        AuthUser authUser = authServer.getUserWithRetry(acctId);
        InvoiceDefine.InvCategory category = InvoiceDefine.InvCategory.of(invoice.getCategory());
        if (category != null) {
            // 若沒有法人承租人則改開個人發票
            if (invoice.getType() == Biz.getType() && (orders.getContract().getMainContract().getCompanyDriver() == null || StringUtils.isBlank(orders.getContract().getMainContract().getCompanyDriver().getVatNumber()))) {
                invoice.setType(Person.getType());
                invoice.setCategory(InvoiceDefine.InvCategory.MEMBER.getCategory());
                invoice.setTitle(authUser.getAcctName());
                invoice.setId(authUser.getLoginId());
            }
            switch (category) {
                case CA:
                case MOBILE:
                case MEMBER:
                    if (invoice.getType() == Person.getType() && (StringUtils.isBlank(invoice.getTitle()) || StringUtils.isBlank(invoice.getId()))) {
                        invoice.setTitle(authUser.getAcctName());
                        invoice.setId(authUser.getLoginId());
                    }
                    break;
                case DONATE:
                    invoice.setTitle(InvoiceDefine.InvCategory.DONATE.getTitle());
                    invoice.setId(Donate.HSAPF.getId());
                    break;
                default:
            }
        }
    }


    /**
     * 發票作廢/折讓
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public List<Invoices> updateInvoice(@NonNull String orderNo, @NonNull String invoiceNo, @NonNull String reason, String memberId) {
        Orders orders = orderService.getOrder(orderNo);
        List<Invoices> invoicesList = invoicesRepository.findInvoicesByOrderNo(orders.getOrderNo());
        for (Invoices unit : invoicesList) {
            if (unit.getInvNo().equals(invoiceNo)) {
                unit = updateInvoice(unit, reason, memberId);
                break;
            }
        }
        return invoicesList;
    }

    @Transactional(transactionManager = "mysqlTransactionManager", propagation = Propagation.REQUIRES_NEW)
    public Invoices updateInvoice(@NonNull Invoices invoices, @NonNull String reason, String memberId) {
        InvoiceDefine.InvStatus status = invoices.getAction();
        if (status == null) {
            throw new BadRequestException("發票已作廢或折讓：" + invoices.getInvNo());
        }

        if (status.equals(InvoiceDefine.InvStatus.INVALIDATE)) {
            Instant createAt = invoices.getCreatedAt().toInstant();
            createAt = createAt.with(ChronoField.MILLI_OF_SECOND, 0);
            InvoiceCancelRequest cancelRequest = new InvoiceCancelRequest();
            cancelRequest.setCancelTransactionID(UUID.randomUUID().toString().substring(0, 29));
            cancelRequest.setPeriodYear(createAt.atZone(DateUtils.ZONE_TPE).get(ChronoField.YEAR) + "");
            cancelRequest.setInvoiceNo(invoices.getInvNo());
            cancelRequest.setCancelDate(Instant.now());
            cancelRequest.setCancelMemo(reason);
            Result<List<InvoiceCancelResponse>> result = finServiceBusClient.cancelInvoice(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, memberId, Collections.singletonList(cancelRequest));
            Map<String, InvoiceCancelResponse> cancelResponseMap = Optional.ofNullable(result).map(Result::getData).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(InvoiceCancelResponse::getCancelTransactionID, o -> o));
            InvoiceCancelResponse response = cancelResponseMap.get(cancelRequest.getCancelTransactionID());
            if (response != null && StringUtils.isBlank(response.getErrorMsg())) {
                invoices.action(status, reason);
                invoices.setCancelCheckOut();
                invoices.setDeletedAt(new Date(response.getCancelDate().toEpochMilli()));
                invoices.setMemberId(memberId);
            } else {
                throw new BadRequestException("發票做廢失敗:" + Optional.ofNullable(response).map(InvoiceCancelResponse::getErrorMsg).orElse("查不到發票"));
            }
        } else if (status.equals(InvoiceDefine.InvStatus.ALLOWANCE)) {
            AllowanceIssueByInvoiceNoRequest allowanceIssueByInvoiceNoRequest = new AllowanceIssueByInvoiceNoRequest();
            allowanceIssueByInvoiceNoRequest.setAllowanceDate(Instant.now());
            allowanceIssueByInvoiceNoRequest.setAllowanceStatus(AllowanceDataEnum.AllowanceStatus.sellerIssue);
            allowanceIssueByInvoiceNoRequest.setAllowanceType(AllowanceDataEnum.AllowanceType.EC);
            allowanceIssueByInvoiceNoRequest.setInvoiceNo(invoices.getInvNo());
            allowanceIssueByInvoiceNoRequest.setPeriodYear(DateUtils.toDateString(invoices.getCreatedAt(), "yyyy", DateUtils.ZONE_TPE));
            allowanceIssueByInvoiceNoRequest.setMemo(reason);
            allowanceIssueByInvoiceNoRequest.setIsSigned(false);
            allowanceIssueByInvoiceNoRequest.setIssueTransactionID(UUID.randomUUID().toString().substring(0, 29));
            Result<AllowanceInfoQueue> result = finServiceBusClient.allowanceInvoice(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, memberId, allowanceIssueByInvoiceNoRequest);
            if (result.getStatusCode() == 0 && result.getData() != null) {
                invoices.action(status, reason);
                invoices.setCancelCheckOut();
                invoices.setDeletedAt(Optional.ofNullable(result.getData()).map(AllowanceInfoQueue::getAllowanceDate).map(Instant::toEpochMilli).map(Date::new).orElse(new Date()));
                invoices.setAllowanceNo(Optional.ofNullable(result.getData()).map(AllowanceInfoQueue::getAllowanceNo).orElse(null));
                invoices.setMemberId(memberId);
            } else {
                throw new BadRequestException("發票折讓失敗:" + Optional.ofNullable(result).map(Result::getData).map(AllowanceInfoQueue::getErrMsg).orElse("查不到發票"));
            }
        }
        invoicesRepository.saveAndFlush(invoices);
        return invoices;
    }

    /**
     * 設定為不用登打
     */
    @Transactional(transactionManager = "mysqlTransactionManager", propagation = Propagation.REQUIRES_NEW)
    @DelAll(group = Invoices.class)
    public void setCheckout(List<Invoices> invoicesList) {
        invoicesRepository.saveAll(invoicesList.stream().peek(i -> {
            i.setIsCheckout(InvoiceDefine.InvCheckoutStatus.NONE_CHECKOUT.getStatus());
            i.setIsFinBus(false);
        }).collect(Collectors.toList()));
    }


    /**
     * 異動發票與費用明細關聯
     */
    public void updateInvoiceRefPriceInfoIds(String orderNo, @NonNull List<InvoicePriceInfoUpdateRequest> invoicesReq, String memberId) {
        Orders orders = orderService.getOrder(orderNo);
        Map<String, Invoices> invoicesMap = invoicesRepository.findInvoicesByOrderNo(orders.getOrderNo()).stream().collect(Collectors.toMap(Invoices::getInvNo, Function.identity()));
        InvoiceInfo invoiceInfo = new InvoiceInfo(new ArrayList<>(invoicesMap.values()));
        AtomicInteger total = new AtomicInteger(invoiceInfo.getTotalAmount());  // 總發票金額
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(orderNo);
        int paidAmt = priceInfoWrapper.toFilter().currentReceivable().paid().wrap().getActualPrice();
        invoicesReq.forEach(request -> {
            Invoices invoices = Optional.ofNullable(invoicesMap.get(request.getInvoiceNo())).orElseThrow(() -> new BadRequestException(String.format("%s找不到該發票", request.getInvoiceNo())));
            request.setAmount(invoices.getAmount());
            // 檢核發票費用
            invoicePriceInfoValidate(request.getRefPriceInfoIds(), request.getAmount(), priceInfoWrapper.getList());

            invoices.setRefPriceInfoIds(request.getRefPriceInfoIds());
            invoices.setMemberId(memberId);
        });

        if (total.get() != paidAmt) {

            log.error(("發票開立總金額:" + total + ", 已收總金額:" + paidAmt + " 不一致"));
            throw new BadRequestException("發票開立總金額:" + total + ", 已收總金額:" + paidAmt + " 不一致");
        }
        invoicesRepository.saveAll(invoicesMap.values());
    }

    /**
     * 是否與系統時間在同一日
     **/
    private boolean checkSysDate(@Nullable Date benchmark) {
        if (!checkSysDate) {
            return true;
        }
        if (Objects.isNull(benchmark)) {
            return false;
        }
        ZoneId zoneId = ZoneId.of("Asia/Taipei");
        LocalDate sys = LocalDate.now(zoneId);
        LocalDate b = benchmark.toInstant().atZone(zoneId).toLocalDate();
        return sys.isEqual(b);
    }

    private void setCityArea(InvoiceIssueRequest invoiceIssueRequest, AuthUser user) {
        List<City> cities = goSmartServer.getCityArea();
        StringBuilder address = new StringBuilder();
        if (user.getCityId() != null && user.getAreaId() != null) {
            City city = cities.stream().filter(c -> c.getCityId() == user.getCityId()).findAny().orElse(null);
            if (city != null) {
                address.append(city.getCityName());
                if (city.getArea() != null) {
                    Optional.ofNullable(city.getArea()).orElse(Collections.emptyList()).stream()
                        .filter(area -> area.getAreaId() == user.getAreaId())
                        .findAny().map(Area::getAreaName).ifPresent(address::append);
                    address.append(user.getAddr());
                }
            }
        }
        if (StringUtils.isNotBlank(address.toString())) {
            invoiceIssueRequest.setNotifyBuyerAddress(address.toString());
        }
    }

    public CsvUtil.ByteArrayOutputStream2ByteBuffer generateCsv(InvoiceCriteriaRequest request) {
        request.setLimit(null);
        request.setSkip(null);
        List<InvoiceDTO> invoiceList = getInvoices(request);

        List<InvoiceCSV> csvList = invoiceList.stream().map(InvoiceCSV::new).collect(Collectors.toList());
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CsvUtil.createCsv(
            csvList,
            InvoiceCSV.HEADS,
            true,
            ',',
            out,
            Charset.forName("big5"),
            InvoiceCSV.class
        );
        return out;
    }

    /**
     * 條件式取得發票資訊
     */
    private List<InvoiceDTO> getInvoices(InvoiceCriteriaRequest request) {
        return invoicesRepository.getInvoices(request);
    }

    /**
     * 取得需要作廢/折讓的發票
     */
    public List<Invoices> getNeedCancelInvoice(String orderNo) {
        List<Invoices> invoicesList = getInvoice(orderNo).stream().filter(i -> i.getStatus().equals(InvoiceDefine.InvStatus.CREATE.name())).collect(Collectors.toList());
        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(orderNo);
        List<Invoices> cancelList = new ArrayList<>();
        for (Invoices invoices : invoicesList) {
            if (CollectionUtils.isEmpty(invoices.getRefPriceInfoIds())) {
                cancelList.add(invoices);
                continue;
            }
            int amt = orderPriceInfoList.stream()
                .filter(opi -> invoices.getRefPriceInfoIds().contains(opi.getId())
                    || invoices.getRefPriceInfoIds().contains(opi.getRefPriceInfoNo())).mapToInt(PriceInfoInterface::getActualPrice).sum();
            if (amt != invoices.getAmount()) {
                cancelList.add(invoices);
            }
        }
        return cancelList;
    }

    /**
     * 取得可開立發票的款項
     */
    public List<OrderPriceInfo> getCanCreateInvoicePriceInfo(String orderNo) {
        List<Invoices> invoicesList = getInvoice(orderNo);
        List<Integer> priceInfoIds = new ArrayList<>();
        invoicesList.forEach(i -> Optional.ofNullable(i.getRefPriceInfoIds()).ifPresent(priceInfoIds::addAll));
        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getCurrentReceivablePriceInfo(orderNo).getList();
        return orderPriceInfoList.stream().filter(opi -> !priceInfoIds.contains(opi.getId())).collect(Collectors.toList());
    }


    public CsvUtil.ByteArrayOutputStream2ByteBuffer incomeReport(Date date) {
        if (date == null) {
            date = Date.from(DateUtil.convertToStartOfInstant(Instant.now().atZone(DateUtils.ZONE_TPE).plus(1, ChronoUnit.MONTHS).with(ChronoField.DAY_OF_MONTH, 1).toInstant()).minus(1, ChronoUnit.SECONDS));
        }
        List<Orders> ordersList = orderService.getOrdersByStatus(OrderStatus.DEPART);
        ordersList.addAll(orderService.getOrdersByStatus(OrderStatus.BOOKING));
        Map<String, Orders> ordersMap = ordersList.stream().collect(Collectors.toMap(Orders::getOrderNo, Function.identity()));
        Map<String, Invoices> invoiceMap =
            invoicesRepository.findInvoicesByOrderNos(new ArrayList<>(ordersMap.keySet())).stream().filter(i -> i.getStatus().equals(InvoiceDefine.InvStatus.CREATE.name())).collect(Collectors.toMap(Invoices::getInvNo, Function.identity()));
        List<Integer> orderPriceIds = new ArrayList<>();
        invoiceMap.forEach((k, v) -> Optional.ofNullable(v.getRefPriceInfoIds()).ifPresent(orderPriceIds::addAll));
        Map<Integer, OrderPriceInfo> monthlyFeeMap = priceInfoService.getByIds(orderPriceIds).stream().filter(opi -> opi.getCategory() == MonthlyFee).collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));
        Map<String, List<OrderPriceInfo>> invOrderPriceMap = new HashMap<>();
        invoiceMap.forEach((k, i) -> {
            List<OrderPriceInfo> orderPriceInfoList = new ArrayList<>();
            Optional.ofNullable(i.getRefPriceInfoIds()).ifPresent(ids -> ids.forEach(id -> {
                OrderPriceInfo orderPriceInfo = monthlyFeeMap.get(id);
                if (orderPriceInfo != null) {
                    orderPriceInfoList.add(orderPriceInfo);
                }
            }));
            invOrderPriceMap.put(i.getInvNo(), orderPriceInfoList);
        });
        List<IncomeDTO> result = new ArrayList<>();
        Date finalDate = date;
        invOrderPriceMap.forEach((k, v) -> {

            AtomicReference<IncomeDTO> incomeDTO = new AtomicReference<>(null);
            v.forEach(opi -> {
                if (incomeDTO.get() == null) {
                    Invoices i = invoiceMap.get(k);
                    CalculateStage stage = DateUtil.calculateStageAndDate(ordersMap.get(opi.getOrderNo())).get(opi.getStage() - 1);
                    incomeDTO.set(new IncomeDTO(k, opi.getOrderNo(), i.getCreatedAt(), i.getUnTax(), opi.getReceivedAmount(), opi.getStage(), stage, finalDate));
                } else {
                    incomeDTO.get().addAmount(opi.getActualReceivePrice());
                }
            });
            if (incomeDTO.get() != null && incomeDTO.get().getUnEffectiveAmount() > 0 && incomeDTO.get().getEndDate().toInstant().isAfter(incomeDTO.get().getTargetDate().toInstant())) {
                result.add(incomeDTO.get());
            }
        });

        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CsvUtil.createCsv(
            result,
            IncomeDTO.CSV_HEADER,
            true,
            ',',
            out,
            Charset.forName("big5"),
            IncomeDTO.class
        );
        return out;
    }

    @Del(group = Invoices.class, key = "'invoice.' + #orderNo")
    public void cleanInvoiceCache(String orderNo) {
        // do nothing
    }
}
