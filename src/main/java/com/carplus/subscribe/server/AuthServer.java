package com.carplus.subscribe.server;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import carplus.common.response.exception.BadRequestException;
import carplus.common.response.exception.CarPlusException;
import carplus.common.response.exception.LogicException;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import carplus.common.utils.StringUtils;
import carplus.common.utils.TaiwanIDUtils;
import com.carplus.subscribe.db.mysql.entity.contract.EContractReferencable;
import com.carplus.subscribe.db.mysql.entity.contract.IOrder;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.CreditMechanismType;
import com.carplus.subscribe.enums.CreditRemarkType;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.audit.BlackListQueryRes;
import com.carplus.subscribe.model.audit.UserDocumentsReviewByCashierPUTV2Req;
import com.carplus.subscribe.model.audit.UserDocumentsReviewInternalRes;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.auth.req.AuthDealerUserSaveRequest;
import com.carplus.subscribe.model.auth.req.CustomerDriverPostReq;
import com.carplus.subscribe.model.auth.req.MultiQueryRequest;
import com.carplus.subscribe.model.auth.resp.*;
import com.carplus.subscribe.model.credit.CreditCheckFullResponse;
import com.carplus.subscribe.model.credit.CreditInfo;
import com.carplus.subscribe.model.credit.req.CreditCheckRequest;
import com.carplus.subscribe.model.credit.resp.CreditCheckFullRes;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;

@Slf4j
@Component
public class AuthServer {

    @Value("${carplus.service.auth}")
    private String authUri;
    @Value("${carplus.service.audit}")
    private String auditUri;
    @Autowired
    private ObjectMapper objectMapper;
    @Value("${audit.timeout}")
    private Integer auditTimeout;

    private static final int BATCH_SIZE = 10;

    // 專用執行緒池，用於黑名單檢查的併發操作
    private final ExecutorService blacklistExecutor;

    // 用於追蹤正在進行的使用者查詢，避免對相同 acctId 的重複查詢
    private final ConcurrentHashMap<Integer, CompletableFuture<AuthUser>> ongoingUserRequests = new ConcurrentHashMap<>();

    public AuthServer() {
        // 初始化執行緒池
        int processors = Runtime.getRuntime().availableProcessors();
        this.blacklistExecutor = Executors.newFixedThreadPool(
            Math.min(processors * 2, 20), // 最多 20 個執行緒
            new ThreadFactoryBuilder()
                .setNameFormat("blacklist-check-%d")
                .setDaemon(true)
                .build()
        );
    }

    @PreDestroy
    public void shutdown() {
        blacklistExecutor.shutdown();
        try {
            if (!blacklistExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                blacklistExecutor.shutdownNow();
                if (!blacklistExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    log.error("Blacklist executor did not terminate");
                }
            }
        } catch (InterruptedException e) {
            blacklistExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 紀錄自動授信資訊
     */
    private void recordAutoCreditResult(Orders order, CreditCheckFullResponse response, boolean isCredit, CreditCheckFullRes check) {
        if (isCredit) {
            order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
                .updateAutoCredit(CreditRemarkType.AUTO_CREDIT_SUCCESS, CreditMechanismType.CRIF, null));
        } else {
            List<String> failMessage = null;
            CreditRemarkType autoCreditRemarkType = (response == null || response.getData() == null || response.getData().getPass() == CreditCheckFullRes.Pass.FAIL)
                ? CreditRemarkType.SYSTEM_CREDIT_FAIL :
                CreditRemarkType.AUTO_CREDIT_FAIL;
            if (check != null) {
                if (check.getPass() == CreditCheckFullRes.Pass.FAIL) {
                    autoCreditRemarkType = CreditRemarkType.CREDIT_FAIL;
                } else if (check.getPass() == CreditCheckFullRes.Pass.MANUALLY) {
                    autoCreditRemarkType = CreditRemarkType.AUTO_CREDIT_FAIL;
                }
                if (!check.getDetails().isEmpty()) {
                    failMessage = check.getDetails().stream()
                        .filter(detail -> !detail.getIsValided())
                        .map(CreditCheckFullRes.DetailRes::getMessage)
                        .collect(Collectors.toList());
                }
            } else {
                failMessage = Optional.ofNullable(response)
                    .filter(resp -> StringUtils.isNotEmpty(resp.getMessage()))
                    .map(resp -> Lists.newArrayList(resp.getMessage()))
                    .orElse(null);
            }
            order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
                .updateAutoCredit(autoCreditRemarkType, CreditMechanismType.CRIF, failMessage));
        }
    }

    /**
     * 自動授信
     */
    public boolean doAutoCredit(@NonNull Orders order, AuthUser user) {
        if (Objects.equals(1, user.getIsForeigner()) || !TaiwanIDUtils.validateTaiwanID(user.getLoginId())) {
            order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
                .updateAutoCredit(CreditRemarkType.ID_VALIDATE_FAIL, CreditMechanismType.CRIF,
                    Collections.singletonList(CreditRemarkType.ID_VALIDATE_FAIL.getDescription())));
            return false;
        }

        CreditCheckRequest request = new CreditCheckRequest(order.getOrderNo(), user);
        log.info("訂單編號: {}, 請求授信: {}", order.getOrderNo(), request);
        try {
            return HttpUtils.post(
                    auditUri + "/internal/audit/v2/users/ccis/creditCheck",
                    HttpUtils.Options.custom()
                        .requestConfig(builder -> builder.setSocketTimeout(auditTimeout))
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(request), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    CreditCheckFullResponse response = objectMapper.readValue(res.getEntity().getContent(), CreditCheckFullResponse.class);

                    boolean isCredit = false;
                    CreditCheckFullRes check = null;
                    if (response != null && response.getStatusCode() == 0) {
                        check = response.getData();

                        log.info("訂單編號: {}, 授信結果：{}", order.getOrderNo(), check);
                        if (check != null && check.getPass() == CreditCheckFullRes.Pass.PASS) {
                            isCredit = true;
                        }
                    }
                    // 紀錄自動授信資訊
                    recordAutoCreditResult(order, response, isCredit, check);
                    return isCredit;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for CRIF check error：", e);
            order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
                .updateAutoCredit(CreditRemarkType.SYSTEM_CREDIT_FAIL, CreditMechanismType.CRIF, Lists.newArrayList(e.getMessage())));
        }

        return false;
    }

    /**
     * 取得會員資料 with retry - 防止對相同 acctId 的重複併發查詢
     */
    public AuthUser getUserWithRetry(int acctId) {
        // 檢查是否已有相同 acctId 的查詢正在進行
        CompletableFuture<AuthUser> existingRequest = ongoingUserRequests.get(acctId);
        if (existingRequest != null && !existingRequest.isDone()) {
            try {
                // 等待已存在的請求完成並返回結果
                return existingRequest.get();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new ServerException("Interrupted while waiting for user query", e);
            } catch (ExecutionException e) {
                // 如果原請求失敗，繼續執行新的查詢
                log.debug("Previous request for acctId {} failed, attempting new query", acctId, e);
            }
        }

        // 建立新的非同步查詢
        CompletableFuture<AuthUser> newRequest = CompletableFuture.supplyAsync(() -> getUserWithRetryInternal(acctId, 0));

        // 嘗試放入 map，如果已存在則使用既有的
        CompletableFuture<AuthUser> actualRequest = ongoingUserRequests.putIfAbsent(acctId, newRequest);
        if (actualRequest != null && !actualRequest.isDone()) {
            // 在我們檢查和建立之間，另一個執行緒已經開始查詢
            try {
                return actualRequest.get();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new ServerException("Interrupted while waiting for user query", e);
            } catch (ExecutionException e) {
                // 如果失敗，使用我們的新請求
                actualRequest = newRequest;
            }
        } else {
            actualRequest = newRequest;
        }

        try {
            return actualRequest.get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ServerException("Interrupted while getting user: " + acctId, e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof ServerException) {
                throw (ServerException) cause;
            } else if (cause instanceof CarPlusException) {
                throw (CarPlusException) cause;
            } else {
                throw new ServerException("Failed to get user: " + acctId, cause);
            }
        } finally {
            // 清理完成的請求
            ongoingUserRequests.remove(acctId, actualRequest);
        }
    }

    private AuthUser getUserWithRetryInternal(int acctId, int attempt) {
        try {
            return getUser(acctId);
        } catch (Exception e) {
            if (attempt >= 3) {
                log.error("查詢使用者已嘗試查詢3次皆失敗，不再查詢 acctId={}", acctId, e);
                if (e instanceof ServerException) {
                    throw (ServerException) e;
                } else if (e instanceof CarPlusException) {
                    throw (CarPlusException) e;
                } else {
                    throw new ServerException("Failed after 3 attempts", e);
                }
            }

            // 使用指數退避策略
            long waitTime = (long) Math.pow(2, attempt) * 500; // 500ms, 1s, 2s
            log.debug("Retry attempt {} for acctId {} after {}ms", attempt + 1, acctId, waitTime);

            try {
                Thread.sleep(waitTime);
            } catch (InterruptedException ex) {
                Thread.currentThread().interrupt();
                throw new ServerException("Retry interrupted", ex);
            }

            return getUserWithRetryInternal(acctId, attempt + 1);
        }
    }

    /**
     * 取得會員資料 by account id
     */
    @NonNull
    public AuthUser getUser(int acctId) {
        try {
            return HttpUtils.post(
                auditUri + "/internal/audit/v1/users/multiQuery",
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(new MultiQueryRequest(acctId)), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    MultiQueryResponse response = objectMapper.readValue(res.getEntity().getContent(), MultiQueryResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }

                    if (response.getData() != null && response.getData().getUsers() != null && !response.getData().getUsers().isEmpty()) {
                        return response.getData().getUsers().get(0);
                    }

                    throw new ServerException(String.format("找不到該ACCTID[%d]使用者", acctId));
                })
                .fetch();
        } catch (CarPlusException e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw new ServerException(e);
        }
    }

    /**
     * 取得會員資料 by 身分證+電話
     */
    @Nullable
    public AuthUser getUser(@Nullable String cid, @Nullable String phone) {
        if (StringUtils.isBlank(cid) && StringUtils.isBlank(phone)) {
            return null;
        }

        try {
            return HttpUtils.get(
                    auditUri + "/internal/audit/v1/users/query",
                    HttpUtils.Options.custom().queryString("loginId", StringUtils.trim(cid)).queryString("mainCell", StringUtils.trim(phone)))
                .then(res -> {
                    AuthUserResponse response = objectMapper.readValue(res.getEntity().getContent(), AuthUserResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }

                    return response.getData();
                })
                .fetch();
        } catch (CarPlusException e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw new ServerException(e);
        }
    }

    /**
     * 取得會員資料 by 姓名/電話
     */
    @NonNull
    public List<Integer> getUsers(@Nullable String acctName, @Nullable String phone, @Nullable String idNo) {
        return getUserAcctIds(acctName, phone, idNo).stream().map(AuthUser::getAcctId).collect(Collectors.toList());
    }

    /**
     * 取得多筆會員資料 by accountId ignore Exception
     */
    @NonNull
    public List<AuthUser> getUserAcctIds(Integer... acctIds) {
        try {
            return HttpUtils.post(
                    auditUri + "/internal/audit/v1/users/multiQuery",
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(new MultiQueryRequest(acctIds)), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    MultiQueryResponse response = objectMapper.readValue(res.getEntity().getContent(), MultiQueryResponse.class);

                    return Optional.ofNullable(response)
                        .filter(r -> r.getStatusCode() == 0)
                        .map(Result::getData)
                        .map(com.carplus.subscribe.model.auth.resp.MultiQueryRes::getUsers)
                        .orElseGet(Lists::newArrayList);
                })
                .fetch();
        } catch (Exception e) {
            log.error("getUsers error：", e);
        }

        return Lists.newArrayList();
    }

    /**
     * 取得會員資料 by 姓名/電話
     */
    @NonNull
    public List<AuthUser> getUserAcctIds(@Nullable String acctName, @Nullable String phone, @Nullable String idNo) {
        if (StringUtils.isBlank(acctName) && StringUtils.isBlank(phone) && StringUtils.isBlank(idNo)) {
            return Lists.newArrayList();
        }

        try {
            HttpUtils.Options options = HttpUtils.Options.custom();
            if (StringUtils.isNotBlank(acctName)) {
                options.queryString("acctName", acctName);
            }
            if (StringUtils.isNotBlank(phone)) {
                options.queryString("mainCell", phone);
            }
            if (StringUtils.isNotBlank(idNo)) {
                options.queryString("loginId", idNo);
            }

            return HttpUtils.get(auditUri + "/internal/audit/v1/users/queries", options)
                .then(res -> {
                    QueriesResponse response = objectMapper.readValue(res.getEntity().getContent(), QueriesResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }

                    return Optional.ofNullable(response.getData())
                        .map(QueriesRes::getList)
                        .orElseGet(Lists::newArrayList);
                })
                .fetch();
        } catch (CarPlusException e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw new ServerException(e);
        }
    }

    /**
     * IVR需求單 會員中心註冊戳記
     **/
    public void ivrSignUp(@NonNull String ivrId, @NonNull String phone) {
        try {
            Map<String, Object> body = Maps.newHashMap();
            body.put("ivrId", ivrId);
            body.put("mainCell", phone);
            HttpUtils.post(authUri + "/internal/auth/v1/users/ivr/fastPass",
                    HttpUtils.Options
                        .custom()
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(body), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result result = objectMapper.readValue(res.getEntity().getContent(), Result.class);
                    if (result.getStatusCode() != 0) {
                        log.error("ivr會員註冊戳記失敗：{}, {}", result.getMessage(), result.getData());
                    }
                    return null;
                })
                .fetch();
        } catch (Exception e) {
            log.error("ivr會員註冊戳記失敗：", e);
        }
    }

    /**
     * 帳號狀態查詢，黑名單 & 各服務停權狀態
     */
    private BlackListQueryRes queryAccountStatus(@NonNull Integer acctId) {
        try {
            return HttpUtils.get(String.format("%s/internal/audit/v1/users/%d/account/status", auditUri, acctId))
                .then(res -> {
                    Result<BlackListQueryRes> blackListQueryResResult = objectMapper.readValue(res.getEntity().getContent(), new TypeReference<Result<BlackListQueryRes>>() {});
                    if (blackListQueryResResult.getStatusCode() != 0) {
                        throw new ServerException(blackListQueryResResult.getMessage());
                    }
                    return blackListQueryResResult.getData();
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for query account status error:", e);
            throw new ServerException(e);
        }
    }

    /**
     * 檢查帳號狀態，若在黑名單或訂閱停權則拋出 SubscribeException
     * 返回給前端 reason pattern: {"inBlackList": Boolean, "subSuspended": Boolean}
     */
    public void verifyAccountStatus(@NonNull Integer acctId) {
        BlackListQueryRes res = queryAccountStatus(acctId);

        // 若在黑名單或訂閱停權有任一狀態為 true 則拋出異常
        if (Boolean.TRUE.equals(res.getInBlackList()) || Boolean.TRUE.equals(res.getSubSuspended())) {
            Map<String, Object> reasonMap = new LinkedHashMap<>();
            reasonMap.put(BlackListQueryRes.Fields.inBlackList, res.getInBlackList());
            reasonMap.put(BlackListQueryRes.Fields.subSuspended, res.getSubSuspended());

            String reasonJson;
            try {
                reasonJson = objectMapper.writeValueAsString(reasonMap);
            } catch (JsonProcessingException e) {
                throw new RuntimeException("無法將帳號狀態序列化為 JSON", e);
            }

            throw new SubscribeException(HttpStatus.OK, BACK_LIST, reasonJson);
        }
    }

    /**
     * 檢查帳號狀態，若在黑名單或訂閱停權則返回 true，其餘情況返回 false
     */
    public boolean isAccountStatusNotAllowed(@NonNull Integer acctId) {
        BlackListQueryRes res = queryAccountStatus(acctId);
        return Boolean.TRUE.equals(res.getInBlackList())
            || Boolean.TRUE.equals(res.getSubSuspended());
    }

    /**
     * 檢查黑名單
     */
    @Nullable
    private Boolean checkBlackList(@Nullable String idNo, @Nullable Integer acctId) {
        try {
            HttpUtils.Options options = HttpUtils.Options.custom();
            if (StringUtils.isNotBlank(idNo)) {
                options.queryString("loginId", idNo);
            } else if (Objects.nonNull(acctId)) {
                options.queryString("acctId", String.valueOf(acctId));
            }
            return HttpUtils.get(auditUri + "/internal/audit/v1/users/blacklist/verify", options)
                .then(res -> {
                    Result<List<Map<Object, Object>>> result = objectMapper.readValue(res.getEntity().getContent(), new TypeReference<Result<List<Map<Object, Object>>>>() {});
                    if (result.getStatusCode() == 0 && result.getData().isEmpty()) {
                        return true;
                    }
                    if (result.getStatusCode() == 0 && !result.getData().isEmpty()) {
                        log.warn("身份證字號或會員帳號為黑名單, pid={}, acctId={}", idNo, acctId);
                        return false;
                    }
                    log.info("身份證字號或會員帳號黑名單檢查, pid:{}, acctId:{}, 結果={}", idNo, acctId, result);
                    return null;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for check blacklist error:", e);
        }
        return null;
    }

    /**
     * 檢查黑名單 by pId身分證
     */
    @NonNull
    public List<String> checkPidBlackList(@NonNull List<String> idNos) {
        if (idNos.isEmpty()) {
            return Collections.emptyList();
        }

        // 分批處理，避免過多併發請求
        List<String> blacklistedPids = Collections.synchronizedList(new ArrayList<>());

        for (int i = 0; i < idNos.size(); i += BATCH_SIZE) {
            List<String> batch = idNos.subList(i, Math.min(i + BATCH_SIZE, idNos.size()));

            // 等待當前批次完成
            CompletableFuture.allOf(batch.stream()
                .map(pid -> CompletableFuture.runAsync(() -> {
                    Boolean result = checkBlackList(pid, null);
                    if (result != null && !result) {
                        blacklistedPids.add(pid);
                    }
                }, blacklistExecutor)
                    .exceptionally(ex -> {
                        log.error("Error checking blacklist for pid: {}", pid, ex);
                        return null;
                    })).toArray(CompletableFuture[]::new)).join();
        }

        return blacklistedPids;
    }

    /**
     * 檢查黑名單 by acctId會員
     */
    @NonNull
    public List<Integer> checkAcctBlackList(@NonNull List<Integer> acctIds) {
        if (acctIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 分批處理，避免過多併發請求
        List<Integer> blacklistedAccounts = Collections.synchronizedList(new ArrayList<>());

        for (int i = 0; i < acctIds.size(); i += BATCH_SIZE) {
            List<Integer> batch = acctIds.subList(i, Math.min(i + BATCH_SIZE, acctIds.size()));

            // 等待當前批次完成
            CompletableFuture.allOf(batch.stream()
                .map(acctId -> CompletableFuture.runAsync(() -> {
                    Boolean result = checkBlackList(null, acctId);
                    if (result != null && !result) {
                        blacklistedAccounts.add(acctId);
                    }
                }, blacklistExecutor)
                    .exceptionally(ex -> {
                        log.error("Error checking blacklist for acctId: {}", acctId, ex);
                        return null;
                    })).toArray(CompletableFuture[]::new)).join();
        }

        return blacklistedAccounts;
    }

    /**
     * 駕駛轉換成會員
     */
    @NonNull
    public DriverDTOResponse driverToUsers(CustomerDriverPostReq customerDriverPostReq) {
        try {
            return HttpUtils.post(
                    auditUri + "/internal/audit/v1/users/driver/convert",
                    HttpUtils.Options.custom()
                        .header("X-System-Kind", HeaderDefine.SystemKind.CASHIER)
                        .header("X-Platform", HeaderDefine.Platform.SERVER)
                        .header("X-MemberId", "SUB")
                        .entity(() -> {
                            StringEntity stringEntity = new StringEntity(objectMapper.writeValueAsString(customerDriverPostReq), StandardCharsets.UTF_8);
                            stringEntity.setContentType("application/json");
                            return stringEntity;
                        }))
                .then(res -> objectMapper.readValue(res.getEntity().getContent(), DriverDTOResponse.class))
                .fetch();
        } catch (Exception e) {
            log.error("getUsers error：", e);
        }

        return null;
    }

    /**
     * 經銷商客戶資料查詢
     */
    @NonNull
    public List<AuthDealerUserResponse> getDealerUsers(@NonNull AuthDealerUserUnionQuery query) {
        HttpUtils.Options options = HttpUtils.Options.custom();
        options.header("X-MemberId", "SUB")
            .header("X-Platform", HeaderDefine.Platform.SERVER)
            .header("X-System-Kind", HeaderDefine.SystemKind.SUB)
            .entity(() -> {
                StringEntity stringEntity = new StringEntity(objectMapper.writeValueAsString(query), StandardCharsets.UTF_8);
                stringEntity.setContentType("application/json");
                return stringEntity;
            });

        try {
            return HttpUtils.post(auditUri + "/internal/audit/v1/seaLand/users/unionQuery", options)
                .then(res -> {
                    QueryAuthDealerUserResponse response = objectMapper.readValue(res.getEntity().getContent(), QueryAuthDealerUserResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }

                    return Optional.ofNullable(response.getData()).map(QueryAuthDealerUserRes::getUsers).orElseGet(ArrayList::new);
                })
                .fetch();
        } catch (Exception e) {
            log.error("getDealerUsers error：", e);
            throw new SubscribeException(DEALER_USER_NOT_FOUND);
        }
    }

    @NonNull
    public AuthUser getAuthUser(DealerOrder dealerOrder) {
        AuthDealerUserResponse dealerUser = getDealerUser(dealerOrder.getDealerUserId());
        if (dealerUser == null) {
            throw new SubscribeException(DEALER_USER_NOT_FOUND);
        }
        List<AuthUser> authUsers = getUserAcctIds(dealerUser.getUserName(), dealerUser.getMainCell(), dealerUser.getIdNo());
        if (authUsers.isEmpty()) {
            throw new SubscribeException(ECONTRACT_ACCOUNT_NOT_EXISTS);
        }
        return authUsers.get(0);
    }

    @NonNull
    public AuthUser getAuthUser(EContractReferencable entity) {
        Integer acctId = entity.getAcctId();

        if (acctId != null) {
            return getUser(acctId);
        } else {
            return getAuthUser((DealerOrder) entity);
        }
    }

    public <T> AuthDealerUserResponse getDealerUser(@NonNull T idNoOrId) throws SubscribeException {
        AuthDealerUserUnionQuery query = new AuthDealerUserUnionQuery();
        if (idNoOrId instanceof String) {
            query.setIdNo(Collections.singletonList((String) idNoOrId));
        } else if (idNoOrId instanceof Long) {
            query.setIds(Collections.singletonList(((Long) idNoOrId).intValue()));
        } else {
            throw new SubscribeException(DEALER_USER_QUERY_PARAMETER_ERROR);
        }
        List<AuthDealerUserResponse> dealerUsers = getDealerUsers(query);
        if (dealerUsers.isEmpty()) {
            return null;
        }

        return dealerUsers.get(0);
    }

    /**
     * 經銷商客戶資訊從 AuthServer 取得
     * Generate DealerUserMap
     */
    public Map<Long, AuthDealerUserResponse> generateDealerUserMap(Set<Long> dealerUserIds) {
        AuthDealerUserUnionQuery query = new AuthDealerUserUnionQuery();
        query.setIds(dealerUserIds.stream().map(Long::intValue).collect(Collectors.toList()));
        return getDealerUsers(query).stream().collect(Collectors.toMap(AuthDealerUserResponse::getId, Function.identity()));
    }

    /**
     * 經銷商客戶資料新增/更新
     */
    @NonNull
    public List<AuthDealerUserResponse> saveDealerUsers(@NonNull AuthDealerUserSaveRequest request) {
        try {
            return HttpUtils.post(auditUri + "/internal/audit/v1/seaLand/users",
                    HttpUtils.Options.custom()
                        .header("X-MemberId", "SUB")
                        .header("X-Platform", HeaderDefine.Platform.SERVER)
                        .header("X-System-Kind", HeaderDefine.SystemKind.SUB)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(request), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    QueryAuthDealerUserResponse response = objectMapper.readValue(res.getEntity().getContent(), QueryAuthDealerUserResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }
                    if (response.getData() != null) {
                        if (CollectionUtils.isNotEmpty(response.getData().getErrorInfos())) {
                            throw new ServerException(response.getData().getErrorInfos().stream()
                                .map(error -> String.format("錯誤訊息: %s", error.getErrorMsg()))
                                .collect(Collectors.joining(",")));
                        } else {
                            return response.getData().getUsers();
                        }
                    }
                    throw new SubscribeException(DEALER_USER_CREATE_FAIL);
                })
                .fetch();
        } catch (CarPlusException e) {
            log.error("call audit-service for save dealer user error：{}", e.getMessage());
            throw LogicException.of(DEALER_USER_CREATE_FAIL, Optional.ofNullable(e.getReason()).orElse("Unknown reason"));
        } catch (Exception e) {
            log.error("saveDealerUsers error：", e);
            throw new SubscribeException(DEALER_USER_CREATE_FAIL);
        }
    }

    /**
     * 使用者簽署最新使用者條款
     */
    @Async
    public void privacyPolicyAccept(String memberId, IOrder order) {
        if (StringUtils.isBlank(order.getDepartTaskId())) {
            return;
        }
        Map<String, String> parameter = new HashMap<>();
        parameter.put("corporation", "CARPLUS");
        parameter.put("userOrderNo", order.getOrderNo());
        try {
            Integer acctId = order instanceof Orders
                ? ((Orders) order).getContract().getMainContract().getAcctId()
                : getAuthUser((DealerOrder) order).getAcctId();
            parameter.put("acctId", acctId.toString());
            HttpUtils.post(
                    String.format("%s/internal/auth/v3/agreements/users/accept", authUri),
                    HttpUtils.Options.custom()
                        .header("X-Platform", HeaderDefine.Platform.SERVER)
                        .header("X-MemberId", memberId)
                        .header("X-System-Kind", HeaderDefine.SystemKind.CASHIER)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(parameter), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result<?> result = objectMapper.readValue(res.getEntity().getContent(), Result.class);
                    log.info("approval Policy response: {}", result);
                    return result;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for approval Policy error：{}", e.getMessage(), e);
        }
    }

    /**
     * 拿取使用者證件照
     */
    @Nullable
    public UserDocumentsReviewInternalRes getAuditPreSignedReview(Integer acctId) {
        try {
            HttpUtils.Options options = HttpUtils.Options.custom();
            options.queryString("acctId", String.valueOf(acctId))
                .queryString("systemKind", HeaderDefine.SystemKind.CASHIER);
            return HttpUtils.get(auditUri + "/internal/audit/v2/users/documents/review", options)
                .then(res -> {
                    TypeReference<Result<UserDocumentsReviewInternalRes>> typeReference = new TypeReference<Result<UserDocumentsReviewInternalRes>>() {};
                    Result<UserDocumentsReviewInternalRes> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);
                    if (result.getStatusCode() == 0 && result.getData() != null) {
                        return result.getData();
                    }
                    // 查無審核文件當作狀態-1
                    if (Objects.equals(result.getStatusCode(), 1308)) {
                        UserDocumentsReviewInternalRes resp = new UserDocumentsReviewInternalRes();
                        resp.setVerifyStatus(-1);
                        return resp;
                    }
                    log.info("拿取證件結果 fail.{},acctId:{}", result, acctId);
                    throw new BadRequestException("拿取證件結果 fail");
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for document review error:", e);
        }
        return null;
    }

    /**
     * 通過使用者證件照審核
     */
    @Nullable
    public void approveAuditPreSignedReview(UserDocumentsReviewByCashierPUTV2Req req, String memberId) {
        try {
            HttpUtils.Options options = HttpUtils.Options.custom();
            options.header("X-Platform", HeaderDefine.Platform.SERVER);
            options.header("X-System-Kind", HeaderDefine.SystemKind.CASHIER);
            options.header("X-MemberId", memberId);
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.acctId, req.getAcctId().toString());
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.idCardFrontId, req.getIdCardFrontId().toString());
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.idCardBackId, req.getIdCardBackId().toString());
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.driverLicenseFrontId, req.getDriverLicenseFrontId().toString());
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.reviewStatus, req.getReviewStatus().name());
            HttpUtils.put(auditUri + "/internal/audit/v2/users/documents/review/cashier", options)
                .then(res -> {
                    Result<?> result = objectMapper.readValue(res.getEntity().getContent(), Result.class);
                    // statusCode=1316, message=審核已通過，不可重複送審
                    if (result.getStatusCode() == 0 || result.getStatusCode() == 1316) {
                        return result.getData();
                    }
                    log.info("通過證件 fail.{},req:{}", result, req);
                    throw new BadRequestException("通過證件結果 fail");
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for approve document review error:", e);
        }
    }

    /**
     * 通過使用者證件照審核
     */
    @Nullable
    @Async
    public void approveAuditPreSignedReview(Integer acctId, String memberId) {
        UserDocumentsReviewInternalRes reviewInternalRes = getAuditPreSignedReview(acctId);
        if (reviewInternalRes != null
            && reviewInternalRes.getVerifyStatus() != -1
            && reviewInternalRes.getIdCardFrontId() != null
            && reviewInternalRes.getIdCardBackId() != null
            && reviewInternalRes.getDriverLicenceFrontId() != null) {
            UserDocumentsReviewByCashierPUTV2Req req = new UserDocumentsReviewByCashierPUTV2Req();
            req.setAcctId(acctId);
            req.setIdCardFrontId(reviewInternalRes.getIdCardFrontId());
            req.setIdCardBackId(reviewInternalRes.getIdCardBackId());
            req.setDriverLicenseFrontId(reviewInternalRes.getDriverLicenceFrontId());
            req.setReviewStatus(UserDocumentsReviewByCashierPUTV2Req.CashierReviewStatus.PASS);
            approveAuditPreSignedReview(req, memberId);
        }
    }
}
