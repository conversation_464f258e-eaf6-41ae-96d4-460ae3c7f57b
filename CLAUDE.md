# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build System & Development Commands

This is a Maven-based Spring Boot 2.4.13 application with Java 8. Use these commands:

```bash
# Build the project
mvn clean compile

# Run tests
mvn test

# Run specific test class
mvn test -Dtest=ServiceNameTest

# Run with code style checking (Google Java Style)
mvn checkstyle:check

# Build package
mvn clean package

# Run Spring Boot application locally
mvn spring-boot:run

# Code analysis with SonarQube
mvn sonar:sonar
```

## Architecture Overview

### Core Application Structure
- **Main Package**: `com.carplus.subscribe`
- **Application Type**: Spring Boot REST API service for car subscription management
- **Base Package Structure**:
  - `controller/` - REST endpoints organized by domain (cars, contract, payment, etc.)
  - `service/` - Business logic layer
  - `db/mysql/entity/` - JPA entities for database mapping
  - `feign/` - External service clients (CRS, Finance, Insurance, etc.)
  - `model/` - DTOs and request/response objects
  - `enums/` - Business domain enums

### Key Business Domains
1. **Orders & Contracts**: Core subscription order management (`contract/Orders.java`, `contract/Contract.java`)
2. **SKU & Shipments**: Product and delivery management (`contract/Sku.java`, `contract/SkuShipment.java`)
3. **Cars Management**: Vehicle inventory and registration (`cars/` package)
4. **Payment Processing**: Payment handling and invoice generation (`payment/` package)
5. **Pricing**: Dynamic pricing calculation (`priceinfo/` package)

### Database Architecture
- **Primary Database**: MySQL with JPA/Hibernate
- **Entity Hierarchy**: Most entities extend `GeneralEntity` for common audit fields
- **Change Tracking**: Custom entity change logging system in `entity/change/` package
- **Key Entities**:
  - `Orders` - Main subscription orders
  - `SkuShipment` - Product delivery tracking
  - `Contract` - Subscription contracts
  - `Cars` - Vehicle inventory

### External Service Integration
Uses OpenFeign clients for integration with:
- CRS (Car Registration System)
- Finance Service
- Insurance Service
- Task Service
- Payment Gateway

### Configuration Management
- **Main Config**: `application.yml` with environment-specific settings
- **Profiles**: Uses Spring profiles for different environments
- **External Services**: Configured via `carplus.service.*` properties

### Code Quality & Standards
- **Style Guide**: Google Java Style Guide enforced via Checkstyle
- **Line Length**: Max 250 characters
- **Testing**: JUnit for unit tests, located in `src/test/java/`
- **Lombok**: Used extensively for reducing boilerplate

### Key Features
1. **Entity Change Logging**: Automatic tracking of database changes via `EntityListener`
2. **Multi-Database Support**: MySQL primary, SQL Server secondary
3. **Async Processing**: Spring `@EnableAsync` for background tasks
4. **Scheduled Tasks**: `@EnableScheduling` for periodic operations
5. **API Documentation**: SpringDoc OpenAPI 3 available at `/doc/swagger-ui.html`

### Performance Considerations
- **Database Connection**: P6Spy for SQL logging and performance monitoring
- **Connection Pooling**: Configured timeouts and max lifetime
- **Pagination**: Custom pagination for large datasets
- **Feign Timeouts**: 30-second read/connect timeouts for external services

### Testing Strategy
- Unit tests in `src/test/java/` mirror main package structure
- Integration tests for services that interact with external systems
- Test profiles configured for different environments

### Development Guidelines
When working with this codebase:
1. Follow the existing package structure and naming conventions
2. Extend `GeneralEntity` for new database entities
3. Use Lombok annotations to reduce boilerplate
4. Place business logic in service layer, keep controllers thin
5. Use existing Feign clients for external service calls
6. Run checkstyle validation before commits
7. Use constructor injection over field injection for better testability
8. Implement proper exception handling using `@ControllerAdvice` and `@ExceptionHandler`
9. Use `@Valid` for request validation and create custom validators when needed